// Funzione per fermare TUTTI i timer e intervalli attivi
function stopAllTimersAndIntervals() {
    console.log('[CLEANUP] Fermando tutti i timer e intervalli...');
    
    let stoppedCount = 0;
    
    // Timer specifici del gioco
    const gameTimers = [
        'turnTimer', 'player1TimerInterval', 'player2TimerInterval',
        'turnTimerInterval', 'gameTimerInterval', 'totalTimerInterval',
        'cardSoundInterval', 'animationInterval', 'setupInterval',
        'chatVisibilityInterval', 'matchmakingInterval', 'connectionCheckInterval',
        'visibilityCheckInterval', 'periodicCleanupInterval', 'homeSoundCleanupInterval'
    ];
    
    gameTimers.forEach(timerName => {
        if (window[timerName]) {
            clearInterval(window[timerName]);
            clearTimeout(window[timerName]);
            window[timerName] = null;
            stoppedCount++;
            console.log(`[CLEANUP] Timer fermato: ${timerName}`);
        }
    });
    
    // Timer/intervalli con nomi numerici (creati dinamicamente)
    for (let i = 1; i <= 1000; i++) {
        if (window[`timer${i}`] || window[`interval${i}`]) {
            clearInterval(window[`timer${i}`]);
            clearTimeout(window[`timer${i}`]);
            clearInterval(window[`interval${i}`]);
            clearTimeout(window[`interval${i}`]);
            window[`timer${i}`] = null;
            window[`interval${i}`] = null;
            stoppedCount++;
        }
    }
    
    // Ferma tutti gli ID di timeout/interval bassi (primi 50)
    for (let id = 1; id <= 50; id++) {
        clearTimeout(id);
        clearInterval(id);
    }
    
    console.log(`[CLEANUP] Timer/intervalli fermati: ${stoppedCount}`);
    return stoppedCount;
}

// Funzione dedicata per la pulizia completa dei suoni residui
function cleanupAllGameSounds() {
    console.log('[AUDIO CLEANUP] Avvio pulizia completa suoni di gioco...');
    
    let cleanedCount = 0;
    
    // Ferma timer e intervalli audio specifici
    if (window.cardSoundInterval) {
        clearInterval(window.cardSoundInterval);
        window.cardSoundInterval = null;
        cleanedCount++;
        console.log('[AUDIO CLEANUP] cardSoundInterval fermato');
    }
    
    // Ferma istanza corrente del suono card
    if (window.currentCardSound) {
        try {
            window.currentCardSound.pause();
            window.currentCardSound.currentTime = 0;
        } catch (e) {}
        window.currentCardSound = null;
        cleanedCount++;
        console.log('[AUDIO CLEANUP] currentCardSound fermato');
    }
    
    // Ferma cache audio del gameAudio se disponibile
    if (typeof gameAudio !== 'undefined' && gameAudio.audioCache) {
        Object.keys(gameAudio.audioCache).forEach(key => {
            try {
                const audio = gameAudio.audioCache[key];
                if (audio && typeof audio.pause === 'function') {
                    audio.pause();
                    audio.currentTime = 0;
                    cleanedCount++;
                }
            } catch (e) {
                console.warn('[AUDIO CLEANUP] Errore fermando audio cache:', key, e);
            }
        });
        console.log('[AUDIO CLEANUP] Cache gameAudio pulita');
    }
    
    // Ferma TUTTI gli elementi audio nel DOM (inclusi dinamici)
    const allAudioElements = document.querySelectorAll('audio');
    allAudioElements.forEach(audio => {
        try {
            if (!audio.paused) {
                audio.pause();
                audio.currentTime = 0;
                cleanedCount++;
            }
            // Se contiene suoni di gioco, rimuovilo completamente
            if (audio.src && (
                audio.src.includes('card.mp3') || 
                audio.src.includes('dado.mp3') || 
                audio.src.includes('start.mp3') || 
                audio.src.includes('loop.mp3') ||
                audio.src.includes('vittoria.mp3') ||
                audio.src.includes('error.mp3') ||
                audio.src.includes('bonus.mp3')
            )) {
                audio.remove();
                cleanedCount++;
            }
        } catch (e) {
            console.warn('[AUDIO CLEANUP] Errore gestendo elemento audio:', e);
        }
    });
    
    // Ferma altri timer audio che potrebbero esistere
    ['diceAnimationAudio', 'battleStartAudio', 'ambientGameAudio'].forEach(audioVar => {
        if (window[audioVar]) {
            try {
                window[audioVar].pause();
                window[audioVar].currentTime = 0;
                window[audioVar] = null;
                cleanedCount++;
            } catch (e) {}
        }
    });
    
    // Pulisci array di timeout audio se esistono
    if (window.audioTimeouts && Array.isArray(window.audioTimeouts)) {
        window.audioTimeouts.forEach(timeout => clearTimeout(timeout));
        window.audioTimeouts = [];
    }
    
    console.log(`[AUDIO CLEANUP] Pulizia completata: ${cleanedCount} elementi audio fermati/rimossi`);
    
    return cleanedCount;
}

// Funzione per pulizia periodica dei suoni durante la permanenza nella home
function startPeriodicSoundCleanup() {
    // Pulizia ogni 5 secondi per catturare suoni che partono dopo il caricamento
    const cleanupInterval = setInterval(() => {
        const allAudioElements = document.querySelectorAll('audio');
        let cleanedDuringInterval = 0;
        
        allAudioElements.forEach(audio => {
            try {
                // Se contiene suoni di gioco e sta suonando, fermalo
                if (audio.src && !audio.paused && (
                    audio.src.includes('card.mp3') || 
                    audio.src.includes('dado.mp3') || 
                    audio.src.includes('start.mp3') || 
                    audio.src.includes('loop.mp3') ||
                    audio.src.includes('vittoria.mp3') ||
                    audio.src.includes('error.mp3') ||
                    audio.src.includes('bonus.mp3')
                )) {
                    audio.pause();
                    audio.currentTime = 0;
                    audio.remove();
                    cleanedDuringInterval++;
                }
            } catch (e) {}
        });
        
        if (cleanedDuringInterval > 0) {
            console.log(`[HOME-LOGGED] Pulizia periodica: fermati ${cleanedDuringInterval} suoni residui`);
        }
    }, 5000);
    
    // Salva l'intervallo per poterlo fermare se necessario
    window.homeSoundCleanupInterval = cleanupInterval;
    console.log('[HOME-LOGGED] Avviata pulizia periodica dei suoni (ogni 5 secondi)');
}

// Ferma la pulizia periodica (utile quando si naviga via dalla home)
function stopPeriodicSoundCleanup() {
    if (window.homeSoundCleanupInterval) {
        clearInterval(window.homeSoundCleanupInterval);
        window.homeSoundCleanupInterval = null;
        console.log('[HOME-LOGGED] Fermata pulizia periodica dei suoni');
    }
}

// Esponi le funzioni di pulizia globalmente
window.stopAllTimersAndIntervals = stopAllTimersAndIntervals;
window.cleanupAllGameSounds = cleanupAllGameSounds;
window.startPeriodicSoundCleanup = startPeriodicSoundCleanup;
window.stopPeriodicSoundCleanup = stopPeriodicSoundCleanup;

// Script specifico per la pagina home-logged.html
document.addEventListener('DOMContentLoaded', function() {
    // FORZA la visibilità immediata per homelogged
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
    document.body.classList.add('loaded');

    // Rimuovi e disabilita completamente le transizioni per homelogged
    const transitionElement = document.querySelector('.page-transition');
    if (transitionElement) {
        transitionElement.classList.remove('active');
        transitionElement.style.opacity = '0';
        transitionElement.style.visibility = 'hidden';
        transitionElement.style.pointerEvents = 'none';
        transitionElement.style.display = 'none';
    }

    console.log('[HOME-LOGGED] Visibilità forzata e transizioni disabilitate');
    
    // PULIZIA COMPLETA IMMEDIATA: Timer, intervalli e suoni residui
    console.log('[HOME-LOGGED] Avvio pulizia completa timer, intervalli e suoni residui...');
    
    // 1. Ferma TUTTI i timer e intervalli attivi
    const stoppedTimers = stopAllTimersAndIntervals();
    if (stoppedTimers > 0) {
        console.log(`[HOME-LOGGED] Fermati ${stoppedTimers} timer/intervalli residui`);
    }
    
    // 2. Ferma tutti i suoni residui
    const cleanedSounds = cleanupAllGameSounds();
    if (cleanedSounds > 0) {
        console.log(`[HOME-LOGGED] Fermati ${cleanedSounds} suoni residui di gioco`);
    }
    
    // 3. AVVIA PULIZIA PERIODICA per catturare suoni che partono dopo il caricamento
    startPeriodicSoundCleanup();

    // PULIZIA INIZIALE: Rimuovi eventuali animazioni residue dal caricamento
    console.log('[HOME-LOGGED] Pulizia iniziale animazioni residue...');
    
    // Usa il sistema di pulizia animazioni se disponibile
    if (window.animationSystem && typeof window.animationSystem.emergencyCleanup === 'function') {
        window.animationSystem.emergencyCleanup();
        console.log('[HOME-LOGGED] Sistema di pulizia animazioni attivato al caricamento');
    }
    
    // Pulizia manuale di tutti gli elementi di animazione
    const allAnimationElements = document.querySelectorAll([
        '.card-animation-container',
        '.card-deal-animation',
        '.animating-card',
        '.animation-pending',
        '[style*="position: absolute"][style*="z-index"]',
        '.card-trail',
        '.particle-trail',
        '.setup-animation'
    ].join(', '));
    
    let initialRemovedCount = 0;
    allAnimationElements.forEach(element => {
        try {
            element.remove();
            initialRemovedCount++;
        } catch (e) {
            console.warn('[HOME-LOGGED] Errore nella rimozione elemento al caricamento:', e);
        }
    });
    
    if (initialRemovedCount > 0) {
        console.log(`[HOME-LOGGED] Rimossi ${initialRemovedCount} elementi di animazione al caricamento`);
    }

    // FORZA la pulizia del game-container se presente (problema quando si torna da partita)
    const gameContainer = document.getElementById('game-container');
    if (gameContainer) {
        gameContainer.style.display = 'none';
        gameContainer.style.opacity = '0';
        gameContainer.classList.remove('ready-for-play', 'preparing', 'grid-layout');
        console.log('[HOME-LOGGED] Game container nascosto e pulito');
    }

    // Forza la visibilità del contenuto homelogged
    const homepageElement = document.getElementById('homepage');
    if (homepageElement) {
        homepageElement.style.display = 'block';
        homepageElement.style.opacity = '1';
        homepageElement.classList.remove('hiding', 'transition-out');
        homepageElement.classList.add('transition-in');
        console.log('[HOME-LOGGED] Homepage element forzato visibile');
    }

    // Verifica prima nel localStorage se l'utente è loggato
    const loggedInUser = localStorage.getItem('loggedInUser');
    const userData = localStorage.getItem('user');
    const token = localStorage.getItem('token');

    // Verifica che il token esista e sia valido
    if (loggedInUser && userData && token) {
        // L'utente è loggato, procedi
        loadUserDataFromStorage();
        loadRecentGames();
        
        // Inizializza il socket per il matchmaking
        if (typeof initializeSocket === 'function') {
            console.log('[HOME-LOGGED] Token nel localStorage:', localStorage.getItem('token') ? 'Present' : 'Missing');
            
            // Forza la reinizializzazione del socket con il token corretto
            if (window.socket) {
                console.log('[HOME-LOGGED] Socket esistente, disconnessione prima di reinizializzare');
                window.socket.disconnect();
            }
            
            initializeSocket();
            
            // Verifica che il socket sia stato inizializzato con il token
            setTimeout(() => {
                if (window.socket && window.socket.auth) {
                    console.log('[HOME-LOGGED] Socket auth dopo inizializzazione:', window.socket.auth);
                }
            }, 100);
        }
        
        // Carica le partite giornaliere all'avvio
        loadDailyMatches();
        
        // Carica il tab attivo di default per cronologia/statistiche
        setTimeout(() => {
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
                const tabName = activeTab.getAttribute('data-tab');
                if (tabName === 'history') {
                    loadMatchHistory();
                } else if (tabName === 'stats') {
                    loadStatistics();
                }
            }
        }, 100);
        
        setupEventListeners();
    } else {
        // Se non ci sono dati nel localStorage, controlla authUtils
        if (window.authUtils && window.authUtils.isLoggedIn) {
            const isLogged = window.authUtils.isLoggedIn();
            
            // Se l'utente non è loggato, reindirizza alla home page principale
            if (!isLogged) {
                console.log('[HOME-LOGGED] Utente non autenticato, reindirizzo a /');
                window.location.href = '/';
                return;
            }
            
            // Carica i dati dell'utente loggato
            loadUserData();
            
            // Carica le partite recenti
            loadRecentGames();
            
            // Inizializza il socket per il matchmaking
            if (typeof initializeSocket === 'function') {
                initializeSocket();
            }
            
            // Carica le partite giornaliere all'avvio
            loadDailyMatches();
            
            // Carica il tab attivo di default per cronologia/statistiche
            const activeTab = document.querySelector('.tab-button.active');
            if (activeTab) {
                const tabName = activeTab.getAttribute('data-tab');
                if (tabName === 'history') {
                    loadMatchHistory();
                } else if (tabName === 'stats') {
                    loadStatistics();
                }
            }
            
            // Gestisci bottoni e click
            setupEventListeners();
        } else {
            // Fallback nel caso in cui authUtils non sia ancora disponibile
            setTimeout(checkAuthUtils, 100);
        }
    }
});

// Verifica se c'è un redirect da effettuare dalla home principale
document.addEventListener('DOMContentLoaded', function() {
    // Controlla se ci sono parametri di gioco attivi (in questo caso non fare redirect)
    const urlParams = new URLSearchParams(window.location.search);
    
    // Se ci sono parametri da preservare, li trasferisci durante il redirect
    if (urlParams.has('gameId') || urlParams.has('resumeMultiplayer') || urlParams.has('action')) {
        // Non fa nulla, lascia che la pagina gestisca i parametri di gioco
        console.log('[REDIRECT] Parametri di gioco rilevati, nessun redirect necessario');
    }
});

// LISTENER AGGIUNTIVI per pulizia suoni quando si torna alla home-logged
// Listener per eventi di visibilità della pagina
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        // Pagina tornata visibile, pulizia completa
        console.log('[HOME-LOGGED] Pagina tornata visibile, pulizia completa timer e suoni...');
        
        const stoppedTimers = stopAllTimersAndIntervals();
        if (stoppedTimers > 0) {
            console.log(`[HOME-LOGGED] Fermati ${stoppedTimers} timer/intervalli alla riapertura`);
        }
        
        const cleanedSounds = cleanupAllGameSounds();
        if (cleanedSounds > 0) {
            console.log(`[HOME-LOGGED] Fermati ${cleanedSounds} suoni residui alla riapertura`);
        }
    }
});

// Listener per eventi di focus della finestra
window.addEventListener('focus', function() {
    console.log('[HOME-LOGGED] Finestra ricevuto focus, pulizia completa timer e suoni...');
    
    const stoppedTimers = stopAllTimersAndIntervals();
    if (stoppedTimers > 0) {
        console.log(`[HOME-LOGGED] Fermati ${stoppedTimers} timer/intervalli al focus`);
    }
    
    const cleanedSounds = cleanupAllGameSounds();
    if (cleanedSounds > 0) {
        console.log(`[HOME-LOGGED] Fermati ${cleanedSounds} suoni residui al focus`);
    }
});

// Listener per eventi di navigazione (popstate)
window.addEventListener('popstate', function() {
    console.log('[HOME-LOGGED] Navigazione rilevata, pulizia completa timer e suoni...');
    
    const stoppedTimers = stopAllTimersAndIntervals();
    if (stoppedTimers > 0) {
        console.log(`[HOME-LOGGED] Fermati ${stoppedTimers} timer/intervalli alla navigazione`);
    }
    
    const cleanedSounds = cleanupAllGameSounds();
    if (cleanedSounds > 0) {
        console.log(`[HOME-LOGGED] Fermati ${cleanedSounds} suoni residui alla navigazione`);
    }
});

// Listener per quando la pagina sta per essere nascosta/chiusa
window.addEventListener('beforeunload', function() {
    // Ferma TUTTO prima di uscire dalla pagina
    console.log('[HOME-LOGGED] Pulizia finale completa prima di uscire dalla pagina...');
    
    // Ferma la pulizia periodica
    stopPeriodicSoundCleanup();
    
    // Ferma tutti i timer/intervalli
    stopAllTimersAndIntervals();
    
    // Ultima pulizia dei suoni
    cleanupAllGameSounds();
    
    console.log('[HOME-LOGGED] Pulizia finale completata');
});

/**
 * Carica i dati utente dal localStorage
 */
function loadUserDataFromStorage() {
    try {
        const userData = JSON.parse(localStorage.getItem('user') || '{}');
        if (userData.username) {
            // Aggiorna l'UI con i dati dell'utente
            updateUIWithUserData(userData);
        }
    } catch (error) {
        console.error('Errore nel parsing dei dati utente:', error);
    }
}

/**
 * Aggiorna l'interfaccia con i dati dell'utente
 */
function updateUIWithUserData(user) {
    if (!user) return;
    
    // Chiama la funzione esistente loadUserData
    loadUserData();
    
    // Aggiorna anche la hero section
    updateHeroSection(user);
}

/**
 * Controlla se authUtils è disponibile e riprova se non lo è
 */
function checkAuthUtils() {
    // Prima controlla nel localStorage
    const loggedInUser = localStorage.getItem('loggedInUser');
    if (loggedInUser) {
        loadUserDataFromStorage();
        loadRecentGames();
        setupEventListeners();
        return;
    }
    
    if (window.authUtils && window.authUtils.isLoggedIn) {
        const isLogged = window.authUtils.isLoggedIn();
        
        if (!isLogged) {
            console.log('[HOME-LOGGED] checkAuthUtils - Utente non autenticato, reindirizzo a /');
            window.location.href = '/';
            return;
        }
        
        // Carica i dati dell'utente loggato
        loadUserData();
        loadRecentGames();
        setupEventListeners();
    } else {
        setTimeout(checkAuthUtils, 100);
    }
}

/**
 * Carica i dati dell'utente loggato
 */
function loadUserData() {
    const user = window.authUtils.getCurrentUser();
    if (!user) return;
    
    // Mostra informazioni nel profilo della sidebar
    updateSidebarProfile(user);
    
    // Aggiorna la hero section
    updateHeroSection(user);
    
    // Mostra le feature boxes per utenti loggati
    const featureBoxes = document.querySelector('.feature-boxes');
    if (featureBoxes) {
        featureBoxes.style.display = 'flex';
    }
}



/**
 * Aggiorna il profilo nella sidebar
 */
function updateSidebarProfile(user) {
    // Aggiorna username nella sidebar
    const usernameDisplay = document.getElementById('username-display');
    if (usernameDisplay) {
        usernameDisplay.textContent = user.username || 'Giocatore';
    }
    
    // Aggiorna rank nella sidebar
    const userRank = document.getElementById('user-rank');
    if (userRank) {
        const rating = user.rating || 1000;
        let rankName = 'Principiante';
        
        if (rating >= 2700) {
            rankName = 'Super Gran Maestro';
        } else if (rating >= 2500) {
            rankName = 'Gran Maestro';
        } else if (rating >= 2400) {
            rankName = 'Maestro Internazionale';
        } else if (rating >= 2200) {
            rankName = 'Maestro';
        } else if (rating >= 2000) {
            rankName = 'Candidato Maestro';
        } else if (rating >= 1800) {
            rankName = 'Dilettante A';
        } else if (rating >= 1600) {
            rankName = 'Dilettante B';
        } else if (rating >= 1400) {
            rankName = 'Dilettante C';
        } else if (rating >= 1200) {
            rankName = 'Dilettante D';
        }
        
        userRank.textContent = rankName;
    }
    
    // Mostra il profilo utente e nascondi i pulsanti di autenticazione
    const authButtons = document.getElementById('auth-buttons');
    const userProfile = document.getElementById('user-profile');
    
    if (authButtons) {
        authButtons.style.display = 'none';
    }
    
    if (userProfile) {
        userProfile.style.display = 'block';
    }
    
    // Aggiorna tooltip dell'utente nel menu di gioco
    const userTooltip = document.getElementById('user-tooltip-text');
    if (userTooltip) {
        userTooltip.textContent = user.username || 'Utente';
    }
}

/**
 * Carica le partite recenti dell'utente
 */
function loadRecentGames() {
    // Questa funzione verrà implementata per caricare le partite recenti dell'utente
    // Richiederà un'integrazione con il backend per ottenere i dati delle partite
    
    // Implementazione di esempio per mostrare la struttura
    const user = window.authUtils.getCurrentUser();
    if (!user) return;
    
    // In una versione reale, questa sarà una chiamata API al backend
    console.log('Caricamento partite recenti per utente:', user.username);
    
    // Le partite recenti possono essere visualizzate in una sezione apposita nella pagina
}

/**
 * Aggiorna la hero section con i dati dell'utente
 */
function updateHeroSection(user) {
    if (!user) return;
    
    // Aggiorna il nome utente nella hero
    const heroUsername = document.getElementById('hero-username');
    if (heroUsername) {
        heroUsername.textContent = user.username || 'Giocatore';
    }
    
    // Calcola e imposta il livello/rank in base al rating
    const rating = user.rating || 1000;
    const heroRank = document.getElementById('hero-rank');
    
    if (heroRank) {
        let rankName = 'Principiante';
        
        if (rating >= 2700) {
            rankName = 'Super Gran Maestro';
        } else if (rating >= 2500) {
            rankName = 'Gran Maestro';
        } else if (rating >= 2400) {
            rankName = 'Maestro Internazionale';
        } else if (rating >= 2200) {
            rankName = 'Maestro';
        } else if (rating >= 2000) {
            rankName = 'Candidato Maestro';
        } else if (rating >= 1800) {
            rankName = 'Dilettante A';
        } else if (rating >= 1600) {
            rankName = 'Dilettante B';
        } else if (rating >= 1400) {
            rankName = 'Dilettante C';
        } else if (rating >= 1200) {
            rankName = 'Dilettante D';
        }
        
        heroRank.textContent = rankName;
    }
    
    // Imposta la bandiera della nazionalità
    const heroFlag = document.getElementById('hero-flag');
    if (heroFlag) {
        const country = user.country || 'it'; // Default Italia
        heroFlag.className = `flag-icon flag-icon-${country}`;
        heroFlag.title = getCountryName(country);
    }
    
    // Carica l'avatar dell'utente
    loadUserAvatar(user);
}

/**
 * Carica l'avatar dell'utente nella hero section
 */
function loadUserAvatar(user) {
    const avatarContainer = document.querySelector('.skm-user-avatar');
    if (!avatarContainer) return;
    
    // Calcola il livello in base al rating per determinare l'avatar
    const rating = user.rating || 1000;
    let avatarLevel = 'Principiante';
    
    if (rating >= 2700) {
        avatarLevel = 'Super Gran Maestro';
    } else if (rating >= 2500) {
        avatarLevel = 'Gran Maestro';
    } else if (rating >= 2400) {
        avatarLevel = 'Maestro Internazionale';
    } else if (rating >= 2200) {
        avatarLevel = 'Maestro';
    } else if (rating >= 2000) {
        avatarLevel = 'Candidato Maestro';
    } else if (rating >= 1800) {
        avatarLevel = 'Dilettante A';
    } else if (rating >= 1600) {
        avatarLevel = 'Dilettante B';
    } else if (rating >= 1400) {
        avatarLevel = 'Dilettante C';
    } else if (rating >= 1200) {
        avatarLevel = 'Dilettante D';
    }
    
    // Crea l'immagine dell'avatar
    avatarContainer.innerHTML = '';
    
    const avatar = document.createElement('img');
    avatar.src = `img/avatar/${avatarLevel.replace(' ', '%20')}.webp`;
    avatar.alt = `Avatar ${avatarLevel}`;
    avatar.className = 'skm-avatar-img';
    avatar.onerror = function() {
        // Fallback se l'immagine webp non è supportata
        this.src = `img/avatar/${avatarLevel.replace(' ', '%20')}.png`;
    };
    
    avatarContainer.appendChild(avatar);
}

/**
 * Ottiene il nome del paese dal codice ISO
 */
function getCountryName(countryCode) {
    const countries = {
        'it': 'Italia',
        'us': 'Stati Uniti',
        'gb': 'Regno Unito',
        'fr': 'Francia',
        'de': 'Germania',
        'es': 'Spagna',
        'jp': 'Giappone',
        'cn': 'Cina',
        'br': 'Brasile',
        'ar': 'Argentina',
        // Aggiungi altri paesi se necessario
    };
    
    return countries[countryCode] || 'Sconosciuto';
}

/**
 * Configura event listener per i pulsanti e le interazioni
 */
function setupEventListeners() {
    // Gestione del pulsante di gioco rapido
    const playOnlineButton = document.getElementById('play-online-button');
    if (playOnlineButton) {
        playOnlineButton.addEventListener('click', function() {
            // Avvia la ricerca di una partita online
            startMatchmaking();
        });
    }
    
    // MATCHMAKING TEMPORANEAMENTE DISABILITATO
    // Per riabilitare: cambiare MATCHMAKING_ENABLED da false a true
    const MATCHMAKING_ENABLED = false;

    // Gestione del pulsante di nuova partita
    const newGameButton = document.getElementById('new-game-button');
    if (newGameButton) {
        // Rimuove tutti i listener esistenti clonando il pulsante
        const clonedButton = newGameButton.cloneNode(true);
        newGameButton.parentNode.replaceChild(clonedButton, newGameButton);

        // Aggiungi il nuovo listener
        clonedButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Controlla se il matchmaking è abilitato
            if (!MATCHMAKING_ENABLED) {
                console.log('[MATCHMAKING] Matchmaking disabilitato, redirect a /game con interfaccia completa');
                
                // PULIZIA DATI OBSOLETI: Rimuovi eventuali dati di partite precedenti
                sessionStorage.removeItem('gameData');
                sessionStorage.removeItem('gameState');
                localStorage.removeItem('savedGameState');
                
                console.log('[NEW GAME] Pulizia dati di partite precedenti completata');
                
                // Salva le preferenze in sessionStorage per evitare glitch nell'URL
                sessionStorage.setItem('skemino_full_interface', JSON.stringify({
                    skipAnimations: true,
                    fullInterface: true,
                    timestamp: Date.now()
                }));
                
                // Reindirizza direttamente a /game senza parametri URL
                window.location.href = '/game';
                return;
            }
            
            // Verifica se l'utente è autenticato
            if (!localStorage.getItem('loggedInUser') && (!window.authUtils || !window.authUtils.isLoggedIn())) {
                // Mostra il popup di login
                const loginPopup = document.getElementById('login-popup');
                if (loginPopup) {
                    loginPopup.style.display = 'flex';
                }
                return;
            }
            
            // Prima di mostrare il modal, verifica che il socket sia connesso
            // NON reinizializzare il socket se è già connesso per evitare di perdere la coda
            if (!window.socket || !window.socket.connected) {
                if (typeof initializeSocket === 'function') {
                    console.log('[MATCHMAKING] Socket non connesso, inizializzazione...');
                    console.log('[MATCHMAKING] Token presente:', localStorage.getItem('token') ? 'YES' : 'NO');
                    initializeSocket();
                }
            } else {
                console.log('[MATCHMAKING] Socket già connesso, uso quello esistente');
                console.log('[MATCHMAKING] Socket auth:', window.socket.auth);
                
                // Verifica che il socket abbia il token di autenticazione
                const token = localStorage.getItem('token');
                if (token && (!window.socket.auth || !window.socket.auth.token)) {
                    console.log('[MATCHMAKING] Socket senza token, aggiornamento auth...');
                    window.socket.auth = { token: token };
                    // Forza la riconnessione con il nuovo token
                    window.socket.disconnect();
                    window.socket.connect();
                }
            }
            
            // Mostra il modal di matchmaking
            const matchmakingModal = document.getElementById('matchmaking-modal');
            if (matchmakingModal) {
                matchmakingModal.style.display = 'flex';
                
                // Attendi un momento per la connessione del socket
                setTimeout(() => {
                    // Avvia il matchmaking solo senza navigazione
                    if (window.socket && window.socket.connected) {
                        console.log('[MATCHMAKING] Socket connesso, invio findMatch');
                        console.log('[MATCHMAKING] Token presente:', localStorage.getItem('token') ? 'YES' : 'NO');
                        
                        // Rimuovi eventuali handler precedenti e aggiungi il nuovo
                        window.socket.off('matchFound');
                        window.socket.off('matchmakingError');

                        window.socket.on('matchFound', (data) => {
                            console.log('[MATCHMAKING] Match trovato!', data);

                            // Chiudi il modal
                            matchmakingModal.style.display = 'none';

                            // Mostra il messaggio di successo
                            console.log('[MATCHMAKING] Partita trovata con', data.opponent ? data.opponent.name : 'un avversario');
                            
                            // Salva i dati della partita per il client
                            if (data.gameId) {
                                console.log('[MATCHMAKING] Salvando dati partita con gameId:', data.gameId);
                                console.log('[MATCHMAKING] Dati completi:', data);
                                // Aggiungi timestamp per validità dei dati
                                const dataWithTimestamp = { ...data, timestamp: Date.now() };
                                sessionStorage.setItem('gameData', JSON.stringify(dataWithTimestamp));
                                
                                // Verifica che i dati siano stati salvati correttamente
                                const savedData = sessionStorage.getItem('gameData');
                                console.log('[MATCHMAKING] Dati salvati in sessionStorage:', savedData);
                                
                                // Naviga alla pagina di gioco dopo un breve ritardo
                                setTimeout(() => {
                                    console.log('[MATCHMAKING] Navigando a /game con gameId:', data.gameId);
                                    // Il gameId è già salvato in sessionStorage dal codice sopra
                                    
                                    // Salva anche le preferenze per interfaccia completa
                                    sessionStorage.setItem('skemino_full_interface', JSON.stringify({
                                        skipAnimations: true,
                                        fullInterface: true,
                                        timestamp: Date.now()
                                    }));
                                    
                                    window.location.href = '/game';
                                }, 1000);
                            } else {
                                console.error('[MATCHMAKING] Nessun gameId ricevuto nei dati del match');
                            }
                        });

                        // Gestisci errori di matchmaking senza mostrarli nella hero section
                        window.socket.on('matchmakingError', (errorMessage) => {
                            console.log('[MATCHMAKING ERROR]', errorMessage);

                            // Chiudi il modal
                            matchmakingModal.style.display = 'none';

                            // Non mostrare l'errore nella hero section, usa solo console log
                            console.log('Errore matchmaking (non mostrato nella UI):', errorMessage);
                        });

                        const userData = window.authUtils ? window.authUtils.getCurrentUser() : null;
                        const requestData = {
                            rating: userData?.rating || 1000,
                            username: userData?.username || 'Giocatore'
                        };
                        
                        console.log('[MATCHMAKING] Invio findMatch con dati:', requestData);
                        console.log('[MATCHMAKING] Socket ID:', window.socket.id);
                        console.log('[MATCHMAKING] Socket auth attuale:', window.socket.auth);
                        
                        window.socket.emit('findMatch', requestData);
                    } else {
                        console.error('[MATCHMAKING] Socket non connesso dopo reinizializzazione');
                        matchmakingModal.style.display = 'none';
                        showError('Errore di connessione al server. Ricarica la pagina.');
                    }
                }, 500); // Ritardo per dare tempo al socket di connettersi
            }
        });
    }
    
    // Gestione del pulsante di cancellazione del matchmaking
    const cancelMatchmakingButton = document.getElementById('cancel-matchmaking');
    if (cancelMatchmakingButton) {
        cancelMatchmakingButton.addEventListener('click', function() {
            const matchmakingModal = document.getElementById('matchmaking-modal');
            if (matchmakingModal) {
                matchmakingModal.style.display = 'none';
            }
            
            // Cancella la ricerca se il socket è connesso
            if (window.socket && window.socket.connected) {
                window.socket.emit('cancelMatchmaking');
            }
        });
    }
    
    // Gestione del pulsante di allenamento
    const trainingButton = document.getElementById('training-button');
    if (trainingButton) {
        trainingButton.addEventListener('click', function() {
            // Avvia modalità allenamento contro AI
            startTrainingMode();
        });
    }
    
    // Gestione del pulsante di sfida locale
    const startLocalButton = document.getElementById('start-local-button');
    if (startLocalButton) {
        startLocalButton.addEventListener('click', function() {
            console.log('Click su sfida un amico');
            
            // Usa la funzione esistente del gioco per mostrare la schermata nomi
            if (typeof showPlayerNamesScreen === 'function') {
                showPlayerNamesScreen();
            } else {
                console.error('La funzione showPlayerNamesScreen non è disponibile');
            }
        });
    }
    
    // Gestione del pulsante logout
    const logoutButton = document.getElementById('logout-button');
    if (logoutButton) {
        logoutButton.addEventListener('click', async function() {
            // Disabilita il pulsante per evitare doppi clic
            logoutButton.disabled = true;
            
            // Effettua logout
            if (window.authUtils && window.authUtils.logout) {
                try {
                    // Attendi che il logout venga completato
                    await window.authUtils.logout();
                    
                    // Aggiungi un piccolo ritardo per assicurarsi che tutto sia pulito
                    setTimeout(() => {
                        // Reindirizza alla home dopo logout
                                        console.log('[HOME-LOGGED] Logout completato, reindirizzo a /');
                window.location.href = '/';
                    }, 100);
                } catch (error) {
                    console.error('Errore durante il logout:', error);
                    // Riabilita il pulsante in caso di errore
                    logoutButton.disabled = false;
                }
            }
        });
    }
    
    // Gestione del link alla classifica nella hero
    const heroLeaderboardLink = document.getElementById('hero-leaderboard-link');
    if (heroLeaderboardLink) {
        heroLeaderboardLink.addEventListener('click', function(e) {
            e.preventDefault();
            showLeaderboard();
        });
    }
    
    // Gestione del link alla classifica nella sidebar
    const classificaLink = document.getElementById('classifica-link');
    if (classificaLink) {
        classificaLink.addEventListener('click', function() {
            showLeaderboard();
        });
    }
    
    // Gestione tabs cronologia/statistiche
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // Rimuovi active da tutti i tab
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // Aggiungi active al tab cliccato
            this.classList.add('active');
            
            // Nascondi tutti i contenuti
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // Mostra il contenuto selezionato
            const selectedTab = document.getElementById(`${tabName}-tab`);
            if (selectedTab) {
                selectedTab.style.display = 'block';
                
                // Carica i dati specifici per ogni tab
                if (tabName === 'stats') {
                    loadStatistics();
                } else if (tabName === 'history') {
                    loadMatchHistory();
                }
            }
        });
    });
    
    // Gestione delle icone azione nella hero
    const heroFriendsLink = document.getElementById('hero-friends-link');
    if (heroFriendsLink) {
        heroFriendsLink.addEventListener('click', function(e) {
            e.preventDefault();
            showFriendsSection();
        });
    }
    
    const heroMessagesLink = document.getElementById('hero-messages-link');
    if (heroMessagesLink) {
        heroMessagesLink.addEventListener('click', function(e) {
            e.preventDefault();
            showMessagesSection();
        });
    }
    
    const heroSettingsLink = document.getElementById('hero-settings-link');
    if (heroSettingsLink) {
        heroSettingsLink.addEventListener('click', function(e) {
            e.preventDefault();
            showSettingsSection();
        });
    }
    
    // NON aggiungiamo listener ai pulsanti della schermata nomi perché sono già gestiti dal gioco originale
    // Il confirm-names-button e back-to-menu-button hanno già i loro listener in script.js
}

// Gestione specifica per la navigazione indietro del browser
window.addEventListener('pageshow', function(event) {
    // Questo evento si attiva quando la pagina viene mostrata, incluso quando si naviga indietro
    console.log('[HOME-LOGGED] Evento pageshow rilevato');

    // Assicurati che la pagina sia sempre visibile
    document.body.classList.add('loaded');
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';

    // PULIZIA COMPLETA ANIMAZIONI RESIDUE da partite precedenti
    console.log('[HOME-LOGGED] Pulizia animazioni residue...');
    
    // PULIZIA COMPLETA DEI SUONI RESIDUI DI GIOCO
    console.log('[HOME-LOGGED PAGESHOW] Pulizia suoni di gioco...');
    const cleanedSounds = cleanupAllGameSounds();
    if (cleanedSounds > 0) {
        console.log(`[HOME-LOGGED PAGESHOW] Fermati ${cleanedSounds} suoni residui`);
    }
    
    // Usa il sistema di pulizia animazioni se disponibile
    if (window.animationSystem && typeof window.animationSystem.emergencyCleanup === 'function') {
        window.animationSystem.emergencyCleanup();
        console.log('[HOME-LOGGED] Sistema di pulizia animazioni attivato');
    }
    
    // Pulizia manuale aggiuntiva per elementi problematici
    const animationElements = document.querySelectorAll([
        '.card-animation-container',
        '.card-deal-animation',
        '.animating-card',
        '.animation-pending',
        '[style*="position: absolute"][style*="z-index"]',
        '.card-trail',
        '.particle-trail',
        '.setup-animation'
    ].join(', '));
    
    let removedCount = 0;
    animationElements.forEach(element => {
        try {
            // Ferma eventuali animazioni in corso
            const animations = element.getAnimations ? element.getAnimations() : [];
            animations.forEach(anim => {
                try {
                    anim.cancel();
                } catch (e) {}
            });
            
            element.remove();
            removedCount++;
        } catch (e) {
            console.warn('[HOME-LOGGED] Errore nella rimozione elemento animazione:', e);
        }
    });
    
    if (removedCount > 0) {
        console.log(`[HOME-LOGGED] Rimossi ${removedCount} elementi di animazione residui`);
    }

    // FORZA la pulizia del game-container (cruciale per navigazione indietro da partita)
    const gameContainer = document.getElementById('game-container');
    if (gameContainer) {
        gameContainer.style.display = 'none';
        gameContainer.style.opacity = '0';
        gameContainer.classList.remove('ready-for-play', 'preparing', 'grid-layout');
        
        // Pulisci anche il contenuto interno del game container
        const gameBoard = gameContainer.querySelector('#game-board');
        if (gameBoard) {
            // Rimuovi eventuali elementi di animazione all'interno del board
            const boardAnimations = gameBoard.querySelectorAll('.card-animation-container, .card-deal-animation');
            boardAnimations.forEach(anim => {
                try {
                    anim.remove();
                } catch (e) {}
            });
        }
        
        console.log('[HOME-LOGGED] Game container pulito durante pageshow');
    }

    // Forza la visibilità del contenuto homelogged
    const homepageElement = document.getElementById('homepage');
    if (homepageElement) {
        homepageElement.style.display = 'block';
        homepageElement.style.opacity = '1';
        homepageElement.classList.remove('hiding', 'transition-out');
        homepageElement.classList.add('transition-in');
        console.log('[HOME-LOGGED] Homepage forzato visibile durante pageshow');
    }

    // Rimuovi eventuali transizioni attive
    const transitionElement = document.querySelector('.page-transition');
    if (transitionElement) {
        transitionElement.classList.remove('active');
        transitionElement.style.opacity = '0';
        transitionElement.style.visibility = 'hidden';
        console.log('[HOME-LOGGED] Transizione rimossa durante pageshow');
    }
    
    // Reset flag di animazione globali che potrebbero essere rimasti attivi
    if (typeof window.isSetupAnimating !== 'undefined') {
        window.isSetupAnimating = false;
    }
    if (window.animationState) {
        window.animationState.needsRestart = false;
        window.animationState.cardDealingInProgress = false;
        window.animationState.initialCardPlacementInProgress = false;
    }
    
    console.log('[HOME-LOGGED] Pulizia completa terminata');
    
    // Se la pagina viene caricata dalla cache del browser (navigazione indietro)
    if (event.persisted) {
        console.log('[HOME-LOGGED] Pagina caricata dalla cache, forzo visibilità');
        // Forza la rimozione di qualsiasi stato di caricamento
        setTimeout(() => {
            document.body.classList.add('loaded');
            document.body.style.visibility = 'visible';
            document.body.style.opacity = '1';
        }, 10);
    }
});

// Funzione globale per pulizia manuale delle animazioni (utilizzabile dalla console)
window.cleanupGameAnimations = function() {
    console.log('[MANUAL CLEANUP] Avvio pulizia manuale delle animazioni...');
    
    // Usa il sistema di pulizia animazioni se disponibile
    if (window.animationSystem && typeof window.animationSystem.emergencyCleanup === 'function') {
        window.animationSystem.emergencyCleanup();
    }
    
    // Pulizia manuale completa
    const allAnimationElements = document.querySelectorAll([
        '.card-animation-container',
        '.card-deal-animation',
        '.animating-card',
        '.animation-pending',
        '[style*="position: absolute"][style*="z-index"]',
        '.card-trail',
        '.particle-trail',
        '.setup-animation',
        '#setup-animation'
    ].join(', '));
    
    let removedCount = 0;
    allAnimationElements.forEach(element => {
        try {
            // Ferma tutte le animazioni
            const animations = element.getAnimations ? element.getAnimations() : [];
            animations.forEach(anim => {
                try {
                    anim.cancel();
                } catch (e) {}
            });
            
            element.remove();
            removedCount++;
        } catch (e) {
            console.warn('[MANUAL CLEANUP] Errore nella rimozione elemento:', e);
        }
    });
    
    // PULIZIA COMPLETA SUONI E TIMER AUDIO
    console.log('[MANUAL CLEANUP] Fermando tutti i suoni e timer audio...');
    
    // Ferma timer e intervalli audio
    if (window.cardSoundInterval) {
        clearInterval(window.cardSoundInterval);
        window.cardSoundInterval = null;
        console.log('[MANUAL CLEANUP] cardSoundInterval fermato');
    }
    
    // Ferma istanza corrente del suono
    if (window.currentCardSound) {
        try {
            window.currentCardSound.pause();
            window.currentCardSound.currentTime = 0;
        } catch (e) {}
        window.currentCardSound = null;
        console.log('[MANUAL CLEANUP] currentCardSound fermato');
    }
    
    // Ferma cache audio del gameAudio se disponibile
    if (typeof gameAudio !== 'undefined' && gameAudio.audioCache) {
        Object.keys(gameAudio.audioCache).forEach(key => {
            try {
                const audio = gameAudio.audioCache[key];
                if (audio && typeof audio.pause === 'function') {
                    audio.pause();
                    audio.currentTime = 0;
                }
            } catch (e) {
                console.warn('[MANUAL CLEANUP] Errore fermando audio cache:', key, e);
            }
        });
        console.log('[MANUAL CLEANUP] Cache gameAudio pulita');
    }
    
    // Ferma TUTTI gli elementi audio nel DOM
    const allAudioElements = document.querySelectorAll('audio');
    let stoppedAudioCount = 0;
    allAudioElements.forEach(audio => {
        try {
            if (!audio.paused) {
                audio.pause();
                audio.currentTime = 0;
                stoppedAudioCount++;
            }
            // Rimuovi completamente l'elemento se è dinamico
            if (audio.getAttribute('data-dynamic') === 'true') {
                audio.remove();
            }
        } catch (e) {
            console.warn('[MANUAL CLEANUP] Errore fermando elemento audio:', e);
        }
    });
    
    if (stoppedAudioCount > 0) {
        console.log(`[MANUAL CLEANUP] Fermati ${stoppedAudioCount} elementi audio DOM`);
    }
    
    // Ferma eventuali timer di altri sistemi audio
    if (window.diceAnimationAudio) {
        try {
            window.diceAnimationAudio.pause();
            window.diceAnimationAudio.currentTime = 0;
        } catch (e) {}
        window.diceAnimationAudio = null;
    }
    
    // Pulisci eventuali timeout audio pendenti
    if (window.audioTimeouts) {
        window.audioTimeouts.forEach(timeout => {
            clearTimeout(timeout);
        });
        window.audioTimeouts = [];
    }
    
    // Reset flag globali
    if (typeof window.isSetupAnimating !== 'undefined') {
        window.isSetupAnimating = false;
    }
    if (window.animationState) {
        window.animationState = {
            needsRestart: false,
            cardDealingInProgress: false,
            initialCardPlacementInProgress: false,
            battleAnimation: { inProgress: false }
        };
    }
    
    // Pulisci game container se presente
    const gameContainer = document.getElementById('game-container');
    if (gameContainer) {
        gameContainer.style.display = 'none';
        gameContainer.style.opacity = '0';
        gameContainer.classList.remove('ready-for-play', 'preparing', 'grid-layout');
    }
    
    console.log(`[MANUAL CLEANUP] Pulizia completata. Rimossi ${removedCount} elementi.`);
    return removedCount;
};

// Intercetta e disabilita qualsiasi tentativo di attivare transizioni in homelogged
const originalAddEventListener = EventTarget.prototype.addEventListener;
EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'beforeunload' && document.querySelector('.skemino-main_wrapper')) {
        // Disabilita i listener beforeunload per homelogged
        console.log('[HOME-LOGGED] Listener beforeunload disabilitato per evitare transizioni');
        return;
    }
    return originalAddEventListener.call(this, type, listener, options);
};

// Monitora e previeni l'attivazione di transizioni e conflitti game-container
setInterval(() => {
    // Controlla e disabilita transizioni
    const transitionElement = document.querySelector('.page-transition');
    if (transitionElement && transitionElement.classList.contains('active')) {
        console.log('[HOME-LOGGED] Transizione rilevata e disabilitata');
        transitionElement.classList.remove('active');
        transitionElement.style.opacity = '0';
        transitionElement.style.visibility = 'hidden';
        document.body.style.visibility = 'visible';
        document.body.style.opacity = '1';
    }

    // Controlla e risolvi conflitti game-container vs homepage
    const gameContainer = document.getElementById('game-container');
    const homepage = document.getElementById('homepage');

    if (gameContainer && homepage) {
        // Se game-container è visibile ma siamo in homelogged, nascondilo
        const gameContainerVisible = gameContainer.style.display !== 'none' &&
                                    gameContainer.offsetHeight > 0;
        const homepageHidden = homepage.style.display === 'none' ||
                              homepage.style.opacity === '0';

        if (gameContainerVisible || homepageHidden) {
            console.log('[HOME-LOGGED] Conflitto rilevato - correzione game-container/homepage');

            // Nascondi game-container
            gameContainer.style.display = 'none';
            gameContainer.style.opacity = '0';
            gameContainer.classList.remove('ready-for-play', 'preparing');

            // Mostra homepage
            homepage.style.display = 'block';
            homepage.style.opacity = '1';
            homepage.classList.remove('hiding', 'transition-out');
            homepage.classList.add('transition-in');
        }
    }
}, 100);

/**
 * Mostra la schermata della classifica
 */
function showLeaderboard() {
    // Nascondi la home
    const mainContent = document.querySelector('.skemino-main-content');
    if (mainContent) {
        mainContent.style.display = 'none';
    }
    
    // Mostra la classifica
    const classificaScreen = document.getElementById('classifica-screen');
    if (classificaScreen) {
        classificaScreen.style.display = 'block';
        
        // Carica i dati della classifica se esiste la funzione
        if (typeof loadClassifica === 'function') {
            loadClassifica();
        }
    }
}

/**
 * Mostra la sezione amici
 */
function showFriendsSection() {
    console.log('Mostra sezione amici');
    // TODO: Implementare la sezione amici
    alert('Sezione amici in arrivo!');
}

/**
 * Mostra la sezione messaggi
 */
function showMessagesSection() {
    console.log('Mostra sezione messaggi');
    // TODO: Implementare la sezione messaggi
    alert('Sezione messaggi in arrivo!');
}

/**
 * Mostra la sezione impostazioni
 */
function showSettingsSection() {
    console.log('Mostra sezione impostazioni');
    // TODO: Implementare la sezione impostazioni
    alert('Sezione impostazioni in arrivo!');
}

/**
 * Carica e visualizza le partite giornaliere
 */
function loadDailyMatches() {
    const dailyMatchesContent = document.getElementById('daily-matches-content');
    if (!dailyMatchesContent) return;
    
    // Mostra il loader
    const loadingDiv = dailyMatchesContent.querySelector('.daily-matches-loading');
    const listDiv = document.getElementById('daily-matches-list');
    const noMatchesDiv = document.getElementById('no-matches-message');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (listDiv) listDiv.style.display = 'none';
    if (noMatchesDiv) noMatchesDiv.style.display = 'none';
    
    // TODO: sostituire con chiamata API reale
    // Simula il caricamento delle partite (per ora con dati fittizi)
    setTimeout(() => {
        // Nascondi il loader
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        // Per ora mostriamo dati di esempio
        const mockMatches = [
            {
                id: 1,
                opponentName: 'Giocatore123',
                result: 'vittoria',
                score: '45 - 38',
                time: '14:32',
                duration: '12 min'
            },
            {
                id: 2,
                opponentName: 'ProPlayer',
                result: 'sconfitta',
                score: '42 - 48',
                time: '11:20',
                duration: '15 min'
            },
            {
                id: 3,
                opponentName: 'Strategist',
                result: 'vittoria',
                score: '50 - 44',
                time: '09:45',
                duration: '18 min'
            }
        ];
        
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        if (mockMatches.length > 0) {
            // Mostra la lista delle partite
            if (listDiv) {
                listDiv.innerHTML = `
                    <div class="daily-matches-grid">
                        ${mockMatches.map(match => `
                            <div class="daily-match-card ${match.result}">
                                <div class="match-header">
                                    <span class="match-time">${match.time}</span>
                                    <span class="match-duration">${match.duration}</span>
                                </div>
                                <div class="match-body">
                                    <div class="match-players">
                                        <span class="player-name">Tu</span>
                                        <span class="vs">VS</span>
                                        <span class="opponent-name">${match.opponentName}</span>
                                    </div>
                                    <div class="match-score">${match.score}</div>
                                    <div class="match-result ${match.result}">
                                        ${match.result === 'vittoria' ? '<i class="fas fa-trophy"></i> Vittoria' : '<i class="fas fa-times-circle"></i> Sconfitta'}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
                listDiv.style.display = 'block';
            }
        } else {
            // Mostra il messaggio "nessuna partita"
            if (noMatchesDiv) noMatchesDiv.style.display = 'block';
        }
    }, 1500);
}

/**
 * Carica e visualizza la cronologia delle partite
 */
function loadMatchHistory() {
    const historyContainer = document.getElementById('match-history-container');
    if (!historyContainer) return;
    
    // Mostra il loader
    const loadingDiv = historyContainer.querySelector('.match-history-loading');
    const listDiv = document.getElementById('match-history-list');
    const noHistoryDiv = document.getElementById('no-history-message');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (listDiv) listDiv.style.display = 'none';
    if (noHistoryDiv) noHistoryDiv.style.display = 'none';
    
    // TODO: sostituire con chiamata API reale
    // Simula il caricamento della cronologia (per ora con dati fittizi)
    setTimeout(() => {
        // Nascondi il loader
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        // Dati di esempio per la cronologia
        const mockHistory = [
            {
                id: 10,
                date: '15/05/2024',
                time: '18:45',
                opponentName: 'ProPlayer',
                result: 'vittoria',
                score: '48 - 42',
                duration: '16 min'
            },
            {
                id: 9,
                date: '15/05/2024',
                time: '14:20',
                opponentName: 'Strategist',
                result: 'sconfitta',
                score: '44 - 50',
                duration: '18 min'
            },
            {
                id: 8,
                date: '14/05/2024',
                time: '21:10',
                opponentName: 'GamerX',
                result: 'vittoria',
                score: '46 - 41',
                duration: '14 min'
            },
            {
                id: 7,
                date: '14/05/2024',
                time: '16:30',
                opponentName: 'Maestro99',
                result: 'vittoria',
                score: '51 - 45',
                duration: '20 min'
            },
            {
                id: 6,
                date: '13/05/2024',
                time: '19:15',
                opponentName: 'ChampionPlayer',
                result: 'sconfitta',
                score: '39 - 45',
                duration: '12 min'
            },
            {
                id: 5,
                date: '12/05/2024',
                time: '20:00',
                opponentName: 'NewPlayer',
                result: 'vittoria',
                score: '55 - 38',
                duration: '15 min'
            }
        ];
        
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        if (mockHistory.length > 0) {
            // Mostra la tabella della cronologia
            if (listDiv) {
                const tableDiv = listDiv.querySelector('.match-history-table');
                if (tableDiv) {
                    tableDiv.innerHTML = `
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Ora</th>
                                    <th>Avversario</th>
                                    <th>Risultato</th>
                                    <th>Punteggio</th>
                                    <th>Durata</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${mockHistory.map(match => `
                                    <tr class="history-row ${match.result}">
                                        <td>${match.date}</td>
                                        <td>${match.time}</td>
                                        <td class="opponent-name">${match.opponentName}</td>
                                        <td class="match-result">
                                            <span class="result-badge ${match.result}">
                                                ${match.result === 'vittoria' ? 
                                                    '<i class="fas fa-trophy"></i> Vittoria' : 
                                                    '<i class="fas fa-times-circle"></i> Sconfitta'}
                                            </span>
                                        </td>
                                        <td class="match-score">${match.score}</td>
                                        <td>${match.duration}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;
                }
                listDiv.style.display = 'block';
            }
        } else {
            // Mostra il messaggio "nessuna cronologia"
            if (noHistoryDiv) noHistoryDiv.style.display = 'block';
        }
    }, 1500);
}

/**
 * Carica e visualizza le statistiche
 */
function loadStatistics() {
    const statsContainer = document.getElementById('stats-container');
    if (!statsContainer) return;
    
    // Mostra il loader
    const loadingDiv = statsContainer.querySelector('.stats-loading');
    const contentDiv = document.getElementById('stats-content');
    const noStatsDiv = document.getElementById('no-stats-message');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (contentDiv) contentDiv.style.display = 'none';
    if (noStatsDiv) noStatsDiv.style.display = 'none';
    
    // Simula il caricamento delle statistiche
    setTimeout(() => {
        // Dati di esempio per le statistiche
        const mockStats = {
            totalGames: 85,
            wins: 54,
            losses: 31,
            winRate: 63.5,
            avgScore: 46.2,
            avgDuration: '15 min',
            longestWinStreak: 8,
            currentStreak: 3,
            mostPlayedOpponent: 'ProPlayer',
            favoriteCards: ['P7', 'C10', 'F12'],
            bestScore: 68,
            worstScore: 32
        };
        
        if (loadingDiv) loadingDiv.style.display = 'none';
        
        if (mockStats.totalGames > 0) {
            // Mostra le statistiche
            if (contentDiv) {
                contentDiv.innerHTML = `
                    <div class="stats-two-column-layout">
                        <div class="stats-left-column">
                            <div class="stat-card primary">
                                <div class="stat-value">${mockStats.winRate}%</div>
                                <div class="stat-label">Percentuale di vittoria</div>
                                <div class="stat-details">${mockStats.wins}V - ${mockStats.losses}S</div>
                            </div>
                            
                            <div class="stats-mini-grid">
                                <div class="stat-card">
                                    <div class="stat-value">${mockStats.totalGames}</div>
                                    <div class="stat-label">Partite totali</div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-value">${mockStats.avgScore}</div>
                                    <div class="stat-label">Punteggio medio</div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-value">${mockStats.avgDuration}</div>
                                    <div class="stat-label">Durata media</div>
                                </div>
                            </div>
                            
                            <div class="stat-box">
                                <h4><i class="fas fa-fire"></i> Serie di vittorie</h4>
                                <p>Migliore: ${mockStats.longestWinStreak} partite</p>
                                <p>Attuale: ${mockStats.currentStreak} partite</p>
                            </div>
                        </div>
                        
                        <div class="stats-right-column">
                            <div class="stat-box">
                                <h4><i class="fas fa-star"></i> Record personali</h4>
                                <p>Punteggio più alto: ${mockStats.bestScore}</p>
                                <p>Punteggio più basso: ${mockStats.worstScore}</p>
                            </div>
                            
                            <div class="stat-box">
                                <h4><i class="fas fa-user"></i> Avversario più frequente</h4>
                                <p>${mockStats.mostPlayedOpponent}</p>
                            </div>
                            
                            <div class="favorite-cards">
                                <h4><i class="fas fa-heart"></i> Carte preferite</h4>
                                <div class="cards-list">
                                    ${mockStats.favoriteCards.map(card => `
                                        <div class="fav-card">
                                            <img src="img/carte/${card}.webp" alt="${card}" />
                                            <span>${card}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                contentDiv.style.display = 'block';
            }
        } else {
            // Mostra il messaggio "nessuna statistica"
            if (noStatsDiv) noStatsDiv.style.display = 'block';
        }
    }, 1500);
}