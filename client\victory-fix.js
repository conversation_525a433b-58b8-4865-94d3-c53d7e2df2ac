/**
 * Script per correggere il problema del riquadro di vittoria
 * Questo script deve essere incluso dopo script.js
 */

// Funzione per mostrare il riquadro di vittoria
function showVictoryScreen(winner, reason) {
    console.log('Mostrando il riquadro di vittoria...');
    const victoryScreen = document.getElementById('victory-screen');
    
    if (!victoryScreen) {
        console.error('Elemento victory-screen non trovato!');
        return;
    }
    
    // Imposta il nome del vincitore e il motivo della vittoria
    const winnerNameElement = document.getElementById('winner-name');
    const victoryReasonElement = document.getElementById('victory-reason');
    
    if (winnerNameElement) {
        winnerNameElement.textContent = winner || 'Giocatore';
    }
    
    if (victoryReasonElement) {
        victoryReasonElement.textContent = reason || 'Hai vinto la partita!';
    }
    
    // Assicurati che il riquadro di vittoria sia visibile
    victoryScreen.style.display = 'flex';
    
    // Forza il reflow per assicurarsi che il riquadro di vittoria venga mostrato
    void victoryScreen.offsetWidth;
    
    // Riproduci suono di vittoria
    if (window.gameAudio && window.gameAudio.play) {
        window.gameAudio.play('victory', 0.7);
    }
    
    console.log('Riquadro di vittoria mostrato.');
}

// Sovrascrive la funzione updateGameUI per assicurarsi che il riquadro di vittoria venga mostrato
const originalUpdateGameUI = window.updateGameUI;
window.updateGameUI = function(state) {
    // Chiama la funzione originale
    originalUpdateGameUI(state);
    
    // CORREZIONE: Non mostrare il pannello di vittoria durante il ripristino della persistenza
    if (window.isRestoringGameState) {
        console.log('[VICTORY-FIX] Saltato pannello vittoria - ripristino in corso');
        return;
    }
    
    // CORREZIONE AGGIUNTIVA: Non mostrare il pannello se stiamo caricando uno stato persistente
    if (window.isRestoringCards) {
        console.log('[VICTORY-FIX] Saltato pannello vittoria - caricamento carte in corso');
        return;
    }
    
    // CORREZIONE AGGIUNTIVA: Controllo se siamo nella fase iniziale di ripristino
    if (document.querySelector('[data-restoring="true"]')) {
        console.log('[VICTORY-FIX] Saltato pannello vittoria - ripristino DOM in corso');
        return;
    }
    
    // Se il gioco è terminato, mostra il riquadro di vittoria
    if (state.gameOver) {
        console.log('[VICTORY-FIX] Gioco terminato, mostrando il riquadro di vittoria con dettagli completi...');
        setTimeout(() => {
            // Controllo aggiuntivo prima di mostrare il pannello
            if (window.isRestoringGameState || window.isRestoringCards) {
                console.log('[VICTORY-FIX] Annullato pannello vittoria - ripristino ancora in corso');
                return;
            }
            
            const victoryScreen = document.getElementById('victory-screen');
            const winnerNameElement = document.getElementById('winner-name');
            const victoryReasonElement = document.getElementById('victory-reason');
            
            if (victoryScreen && victoryScreen.style.display !== 'flex') {
                console.log('[VICTORY-FIX] Forzando la visualizzazione del riquadro di vittoria con dettagli completi...');
                
                // NUOVO: Imposta il nome del vincitore usando i dati dal server
                if (winnerNameElement && state.winner && state.playerNames) {
                    const winnerName = state.playerNames[state.winner] || 'Giocatore';
                    winnerNameElement.textContent = winnerName;
                    console.log('[VICTORY-FIX] Nome vincitore impostato:', winnerName);
                }
                
                // NUOVO: Imposta la spiegazione dettagliata della vittoria dal server
                if (victoryReasonElement && state.winReason) {
                    // Usa il messaggio dettagliato dal server che include tutte le informazioni ERA1/ERA2/ERA3/ERA4
                    victoryReasonElement.textContent = state.winReason;
                    console.log('[VICTORY-FIX] Motivo vittoria dettagliato impostato:', state.winReason);
                } else if (victoryReasonElement) {
                    // Fallback se non c'è winReason specifico
                    victoryReasonElement.textContent = 'Partita completata';
                    console.log('[VICTORY-FIX] Usato motivo fallback');
                }
                
                // Gestisci le informazioni sui rating se disponibili
                if (state.ratingUpdates) {
                    updateRatingChangesInVictoryScreen(state.ratingUpdates);
                    console.log('[VICTORY-FIX] Rating updates applicati');
                } else {
                    // Nascondi la sezione dei rating se non ci sono dati
                    const ratingChangesElement = document.getElementById('rating-changes');
                    if (ratingChangesElement) {
                        ratingChangesElement.style.display = 'none';
                    }
                }
                
                // Riproduci suono di vittoria
                if (window.gameAudio && window.gameAudio.play) {
                    window.gameAudio.play('victory', 0.7);
                }
                
                victoryScreen.style.display = 'flex';
                console.log('[VICTORY-FIX] Riquadro di vittoria mostrato con dettagli completi');
            }
        }, 500);
    }
};

// Aggiungi un listener per l'evento gameState
socket.on('gameState:victory', function(state) {
    // Se il gioco è terminato, mostra il riquadro di vittoria
    if (state.gameOver) {
        console.log('Ricevuto gameState con gameOver=true, mostrando il riquadro di vittoria...');
        setTimeout(() => {
            const victoryScreen = document.getElementById('victory-screen');
            if (victoryScreen && victoryScreen.style.display !== 'flex') {
                console.log('Forzando la visualizzazione del riquadro di vittoria...');
                
                // Riproduci suono di vittoria
                if (window.gameAudio && window.gameAudio.play) {
                    window.gameAudio.play('victory', 0.7);
                }
                
                victoryScreen.style.display = 'flex';
                
                // Gestisci le informazioni sui rating se disponibili
                if (state.ratingUpdates) {
                    updateRatingChangesInVictoryScreen(state.ratingUpdates);
                } else {
                    // Nascondi la sezione dei rating se non ci sono dati
                    const ratingChangesElement = document.getElementById('rating-changes');
                    if (ratingChangesElement) {
                        ratingChangesElement.style.display = 'none';
                    }
                }
            }
        }, 500);
    }
});

// Funzione per aggiornare le informazioni sui cambiamenti di rating nella schermata di vittoria
function updateRatingChangesInVictoryScreen(ratingUpdates) {
    console.log('Aggiornamento dei cambiamenti di rating:', ratingUpdates);
    
    const winnerRatingElement = document.getElementById('winner-rating');
    const loserRatingElement = document.getElementById('loser-rating');
    
    if (!winnerRatingElement || !loserRatingElement) {
        console.error('Elementi per i rating non trovati nella schermata di vittoria');
        return;
    }
    
    const { winner, loser } = ratingUpdates;
    
    // Ottieni i nomi dei giocatori dal gameState corrente
    let winnerName = 'Vincitore';
    let loserName = 'Perdente';
    
    if (window.currentGameState && window.currentGameState.playerNames) {
        // Mappa gli userId ai nomi dei giocatori
        if (winner && winner.userId && window.currentGameState.playerNames[winner.userId]) {
            winnerName = window.currentGameState.playerNames[winner.userId];
        }
        if (loser && loser.userId && window.currentGameState.playerNames[loser.userId]) {
            loserName = window.currentGameState.playerNames[loser.userId];
        }
        console.log('[VICTORY-FIX] Nomi rating - Vincitore:', winnerName, 'Perdente:', loserName);
    }
    
    // Aggiorna le informazioni del vincitore con il nome
    if (winner) {
        const winnerChange = winner.change > 0 ? `+${winner.change}` : winner.change;
        winnerRatingElement.innerHTML = `<strong>${winnerName}</strong> - Rating: <span class="old-rating">${winner.oldRating}</span> → <span class="new-rating">${winner.newRating}</span> <span class="rating-diff">(${winnerChange})</span>`;
        console.log('[VICTORY-FIX] Aggiornato rating vincitore:', winnerName, winner.oldRating, '→', winner.newRating, winnerChange);
    }
    
    // Aggiorna le informazioni del perdente con il nome
    if (loser) {
        const loserChange = loser.change > 0 ? `+${loser.change}` : loser.change;
        loserRatingElement.innerHTML = `<strong>${loserName}</strong> - Rating: <span class="old-rating">${loser.oldRating}</span> → <span class="new-rating">${loser.newRating}</span> <span class="rating-diff">(${loserChange})</span>`;
        console.log('[VICTORY-FIX] Aggiornato rating perdente:', loserName, loser.oldRating, '→', loser.newRating, loserChange);
    }
    
    // Mostra la sezione dei rating
    const ratingChangesElement = document.getElementById('rating-changes');
    if (ratingChangesElement) {
        ratingChangesElement.style.display = 'block';
        console.log('[VICTORY-FIX] Sezione rating mostrata');
    }
}

// Aggiungi un listener per l'evento DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('victory-fix.js caricato.');
    
    // Configura il pulsante "Nuova Partita" nel victory screen
    setupVictoryScreenButtons();
    
    // Configura il gestore per l'evento gameOver direttamente
    setupGameOverHandler();
});

// Funzione per configurare il gestore dell'evento gameOver
function setupGameOverHandler() {
    // Attendi che il socket sia disponibile
    const checkSocket = () => {
        if (window.socket) {
            console.log('[VICTORY-FIX] Configurando gestore gameOver diretto');
            
            // Gestore diretto per l'evento gameOver per i rating
            window.socket.on('gameOver', (data) => {
                console.log('[VICTORY-FIX] Ricevuto evento gameOver diretto:', data);
                
                // Se ci sono rating updates, aggiornali immediatamente nel victory screen
                if (data.ratingUpdates) {
                    console.log('[VICTORY-FIX] Aggiornamento rating dal gestore diretto gameOver');
                    setTimeout(() => {
                        // Verifica che il victory screen sia visibile
                        const victoryScreen = document.getElementById('victory-screen');
                        if (victoryScreen && victoryScreen.style.display === 'flex') {
                            updateRatingChangesInVictoryScreen(data.ratingUpdates);
                        } else {
                            console.log('[VICTORY-FIX] Victory screen non ancora visibile, rating saranno aggiornati da updateGameUI');
                        }
                    }, 100);
                }
            });
        } else {
            // Riprova dopo un breve ritardo se il socket non è ancora disponibile
            setTimeout(checkSocket, 100);
        }
    };
    
    checkSocket();
}

// Funzione per configurare i pulsanti del victory screen
function setupVictoryScreenButtons() {
    const newGameVictoryButton = document.getElementById('new-game-victory');
    const examineGameVictoryButton = document.getElementById('examine-game-victory');
    
    if (newGameVictoryButton) {
        newGameVictoryButton.addEventListener('click', function() {
            console.log('[VICTORY-FIX] Pulsante Nuova Partita cliccato dal victory screen');
            
            // Nascondi il victory screen
            const victoryScreen = document.getElementById('victory-screen');
            if (victoryScreen) {
                victoryScreen.style.display = 'none';
            }
            
            // Avvia il matchmaking esattamente come fa il pulsante nella sidebar
            startMatchmakingFromVictoryScreen();
        });
    }
    
    if (examineGameVictoryButton) {
        examineGameVictoryButton.addEventListener('click', function() {
            console.log('[VICTORY-FIX] Pulsante Esamina Partita cliccato dal victory screen');
            
            // Nascondi il victory screen
            const victoryScreen = document.getElementById('victory-screen');
            if (victoryScreen) {
                victoryScreen.style.display = 'none';
            }
            
            // Attiva il tab "Analisi" per esaminare la partita
            activateAnalysisTab();
        });
    }
    
    // Configura anche il pulsante di cancellazione del matchmaking
    const cancelMatchmakingButton = document.getElementById('cancel-matchmaking');
    if (cancelMatchmakingButton) {
        // Aggiungi un event listener aggiuntivo per gestire le cancellazioni dal victory screen
        cancelMatchmakingButton.addEventListener('click', function() {
            console.log('[VICTORY-FIX] Matchmaking cancellato');
            
            // Nascondi il modal di matchmaking
            const matchmakingModal = document.getElementById('matchmaking-modal');
            if (matchmakingModal) {
                matchmakingModal.style.display = 'none';
            }

            // Cancella la ricerca se il socket è connesso
            if (window.socket && window.socket.connected) {
                window.socket.emit('cancelMatchmaking');
                console.log('[VICTORY-FIX] Evento cancelMatchmaking inviato al server');
            }
        });
    }
}

// Funzione per avviare il matchmaking dal victory screen (identica a quella della sidebar)
function startMatchmakingFromVictoryScreen() {
    console.log('[VICTORY-FIX] Avvio matchmaking dal victory screen');

    // Verifica se l'utente è autenticato
    if (!localStorage.getItem('loggedInUser') && (!window.authUtils || !window.authUtils.isLoggedIn())) {
        console.log('[VICTORY-FIX] Utente non autenticato');
        // Mostra messaggio di errore o redirect al login
        alert('Devi essere loggato per giocare online');
        return;
    }

    // Trova e mostra il modal di matchmaking
    const matchmakingModal = document.getElementById('matchmaking-modal');
    if (!matchmakingModal) {
        console.error('[VICTORY-FIX] Modal di matchmaking non trovato');
        return;
    }

    console.log('[VICTORY-FIX] Mostrando modal di matchmaking');
    matchmakingModal.style.display = 'flex';

    // Pulisci lo stato della partita precedente
    window.currentGameId = null;
    if (window.multiplayer) {
        window.multiplayer.currentGameId = null;
    }

    // Attendi un momento per la connessione del socket
    setTimeout(() => {
        if (window.socket && window.socket.connected) {
            console.log('[VICTORY-FIX] Socket connesso, invio findMatch');

            // Rimuovi eventuali handler precedenti e aggiungi il nuovo
            window.socket.off('matchFound');
            window.socket.on('matchFound', (data) => {
                console.log('[VICTORY-FIX] Match trovato!', data);

                // Chiudi il modal
                matchmakingModal.style.display = 'none';

                // Salva i dati della partita
                if (data.gameId) {
                    console.log('[VICTORY-FIX] Salvando dati partita con gameId:', data.gameId);
                    // Aggiungi timestamp per validità dei dati
                    const dataWithTimestamp = { ...data, timestamp: Date.now() };
                    sessionStorage.setItem('gameData', JSON.stringify(dataWithTimestamp));

                    // Pulisci lo stato dell'interfaccia completa
                    clearGameState();
                    console.log('[VICTORY-FIX] Stato interfaccia completa pulito');

                    // Riabilita tutti i tab della sidebar
                    setTimeout(() => {
                        enableAllSidebarTabsFromVictory();
                        switchToGiocaTabFromVictory();
                        console.log('[VICTORY-FIX] Passaggio al tab Gioca completato');
                    }, 500);

                    // Emetti evento di inizio partita
                    window.dispatchEvent(new CustomEvent('gameStarted', {
                        detail: {
                            type: 'online',
                            gameId: data.gameId,
                            opponent: data.opponent
                        }
                    }));

                    console.log('[VICTORY-FIX] Match trovato - il flusso delle animazioni inizierà automaticamente');
                } else {
                    console.error('[VICTORY-FIX] Nessun gameId ricevuto');
                }
            });

            // Gestisci errori di matchmaking
            window.socket.on('matchmakingError', (errorMessage) => {
                console.log('[VICTORY-FIX] Errore matchmaking:', errorMessage);

                // Chiudi il modal
                matchmakingModal.style.display = 'none';

                // Mostra errore all'utente
                alert('Errore durante la ricerca di una partita: ' + errorMessage);
            });

            const userData = window.authUtils ? window.authUtils.getCurrentUser() : null;
            const requestData = {
                rating: userData?.rating || 1000,
                username: userData?.username || 'Giocatore'
            };

            console.log('[VICTORY-FIX] Invio findMatch con dati:', requestData);
            window.socket.emit('findMatch', requestData);
        } else {
            console.error('[VICTORY-FIX] Socket non connesso');
            matchmakingModal.style.display = 'none';
            alert('Errore di connessione al server');
        }
    }, 500);
}

// Funzioni helper per gestire i tab dalla vittoria
function enableAllSidebarTabsFromVictory() {
    console.log('[VICTORY-FIX] Riabilitazione di tutti i tab');
    document.querySelectorAll('.sidebar-tabs .tab').forEach(tab => {
        tab.style.pointerEvents = 'auto';
        tab.style.opacity = '1';
        tab.style.cursor = 'pointer';
    });
}

function switchToGiocaTabFromVictory() {
    console.log('[VICTORY-FIX] Passaggio al tab Gioca');

    // Rimuovi active da tutti i tab
    document.querySelectorAll('.sidebar-tabs .tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Nascondi tutti i contenuti dei tab
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // Attiva il tab "Gioca"
    const giocaTab = document.querySelector('.tab[data-tab="gioca"]');
    const giocaContent = document.getElementById('tab-gioca');

    if (giocaTab) {
        giocaTab.classList.add('active');
    }

    if (giocaContent) {
        giocaContent.classList.add('active');
    }
}

function activateAnalysisTab() {
    console.log('[VICTORY-FIX] Attivazione tab Analisi');

    // Rimuovi active da tutti i tab
    document.querySelectorAll('.sidebar-tabs .tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Nascondi tutti i contenuti dei tab
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // Attiva il tab "Analisi"
    const analisiTab = document.querySelector('.tab[data-tab="analisi"]');
    const analisiContent = document.getElementById('tab-analisi');

    if (analisiTab) {
        analisiTab.classList.add('active');
    }

    if (analisiContent) {
        analisiContent.classList.add('active');
    }
}

// Funzione per pulire lo stato del gioco (CORREZIONE LOOP INFINITO)
function clearGameState() {
    // CORREZIONE: Usa una funzione diversa per evitare il loop infinito
    if (typeof window.gameStateManager?.clearGameState === 'function') {
        // Usa la funzione del game state manager se disponibile
        window.gameStateManager.clearGameState();
    } else {
        // Implementazione di base se la funzione non esiste
        console.log('[VICTORY-FIX] Pulizia stato gioco di base');
        window.currentGameState = null;
        window.isGameRunning = false;
        window.isSetupAnimating = false;
        sessionStorage.removeItem('gameState');
        localStorage.removeItem('savedGameState');
        
        // Chiama la funzione esistente nel game-state-manager se disponibile
        if (typeof window.clearGameState !== 'undefined' && window.clearGameState !== clearGameState) {
            try {
                const originalClearFunction = window.clearGameState;
                if (originalClearFunction && originalClearFunction !== clearGameState) {
                    originalClearFunction();
                }
            } catch (e) {
                console.warn('[VICTORY-FIX] Errore chiamando clearGameState originale:', e);
            }
        }
    }
}
