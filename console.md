multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10720 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2389 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2392 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2418 [VISIBILITY] Animazione setup completata o in corso - NON riavvio per evitare scatto doppio
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: hidden  
script.js:1606 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2222 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2243 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2261 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2613 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: visible  
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
script.js:11231 [ID SYNC] ID corretto confermato: 6AYeFLxFhFdCYesqAAAV per username: bruscolino
script.js:11244 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11245 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11246 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11247 [HANDLE GAME STATE] - currentMode: online
script.js:11248 [HANDLE GAME STATE] - window.myPlayerId: 6AYeFLxFhFdCYesqAAAV
script.js:11306 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 1, Nero - Rock 2 su d1. Prossimo giocatore: white. StateTurn: 1
psn-unified.js:1887 [PSN] Skip rilevazione pescate - registrazione dal server in corso
script.js:12138 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12141 [GAME STATE] player1 (white): Array(4)
script.js:12141 [GAME STATE] player2 (black): Array(4)
script.js:12361 [PERMANENT NAMES] Nomi permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"giggio","6AYeFLxFhFdCYesqAAAV":"bruscolino"}
script.js:12383 [PERMANENT COLORS] Colori permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"black","6AYeFLxFhFdCYesqAAAV":"white"}
script.js:12455 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12456 [ANIMATION DEBUG] - isStarting: false
script.js:12457 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12458 [ANIMATION DEBUG] - state.gameId: CJVJ6E
script.js:12459 [ANIMATION DEBUG] - state.gameOver: false
script.js:12460 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12461 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12462 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12463 [ANIMATION DEBUG] - state.mode: online
script.js:12464 [ANIMATION DEBUG] - window.animationsCompletedForGame: CJVJ6E
script.js:12465 [ANIMATION DEBUG] - needsAnimations: false
script.js:14414 [UPDATE UI] originalCurrentPlayerId già presente nello stato: 6AYeFLxFhFdCYesqAAAV
script.js:13598 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13664 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13665 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13721 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14442 [DEBUG] Vantaggio calcolato: 50%
script.js:15887 [HISTORY] Aggiunta mossa #3 alla cronologia
script.js:15927 Tentativo di forzare rendering rating...
script.js:15954 Ratings dopo init: Object
script.js:15963 Aggiornamento avatar player1: player1
script.js:15969 Aggiornamento avatar player2: player2
script.js:14514 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14517 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14532 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: 6AYeFLxFhFdCYesqAAAV
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:14540 [UPDATE UI] Chat visibility aggiornata per partita online
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:14657 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 4
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 4
script.js:4845 [OPPONENT HAND] player2-hand: previousSize=5, currentSize=4, hasPlayedCard=true
script.js:4849 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
script.js:4856 [OPPONENT HAND] Carta giocata dall'avversario: Rock-2
script.js:4904 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-2 da player2-hand
script.js:4914 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
script.js:4867 [OPPONENT HAND] Carta dell'avversario Rock-2 rimossa permanentemente da player2-hand - NON verrà ricreata
script.js:14909 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14910 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:11755 [OPPONENT MARKER] Identificata ultima mossa avversario in d1
script.js:11844 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in d1
script.js:14912 [DEBUG] updateGameUI AFTER renderBoard
script.js:14913 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14914 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14934 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14935 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13087 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1531 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1532 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1533 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1563 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1564 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1565 [TIMER DEBUG] - isMyTurn: true
multiplayer.js:1566 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1569 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1571 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
multiplayer.js:1002 [TIMER] Avvio timer di turno: 60 secondi rimanenti
multiplayer.js:1586 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1587 [TIMER DEBUG] - window: true
multiplayer.js:1588 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1589 [TIMER DEBUG] - player1TotalTime: 1799
multiplayer.js:1590 [TIMER DEBUG] - player2TotalTime: 1783
multiplayer.js:1591 [TIMER DEBUG] - gameState.currentPlayerId: player1
multiplayer.js:1603 [TIMER] Modalità online - currentPlayerId: player1
multiplayer.js:1604 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
multiplayer.js:1617 [TIMER] Avviato timer totale per giocatore 1
psn-unified.js:1237 [PSN] Usando handSize dal server per white: 4 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2389 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2392 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2418 [VISIBILITY] Animazione setup completata o in corso - NON riavvio per evitare scatto doppio
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: hidden  
script.js:1606 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10116 [DRAG START] Inizio trascinamento carta Paper-7 del giocatore white
script.js:10117 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
script.js:10158 [DRAG ONLINE] È il mio turno? true
script.js:10164 [DRAG ONLINE] Il mio colore: white
script.js:10183 [DEBUG] effectiveTurnPlayerId: player1
script.js:10184 [DEBUG] turnPlayer extracted: Object
script.js:10247 Drag Start: Object
script.js:10248 Card suit: Paper Card value: 7 Type of value: string
script.js:10252 Current player hand: Array(4)
script.js:10258 Full hand data: [{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-J","suit":"Paper","value":"J"},{"id":"Paper-7","suit":"Paper","value":"7"}]
script.js:10287 Card data being sent: Object
script.js:5314 [CELL DROP LISTENER] Evento drop catturato su cella: cell-c1 Event target: cell-c1
script.js:10391 [DROP SUCCESS] Carta rilasciata su cella: cell-c1 Drop ID: 1751038928668_8ins5pdut Timestamp: 2025-06-27T15:42:08.668Z
script.js:10418 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751038928668_8ins5pdut
script.js:10427 Dropped Card Data: Object
script.js:10445 [DROP ONLINE] È il mio turno? true
script.js:10451 [DROP ONLINE] Il mio colore: white
script.js:10470 [DROP DEBUG] effectiveTurnPlayerId: player1
script.js:10471 [DROP DEBUG] turnPlayer extracted: Object
script.js:10622 (white) tenta di piazzare [DRAG] 7 di Paper su c1
script.js:10640 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751038928668_8ins5pdut
script.js:10700 [DROP] Carta temporanea aggiunta alla cella: c1
script.js:978 [OPPONENT MARKER] Rimuovendo marker esistente
script.js:10706 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
script.js:10339 [DRAG END] DropEffect: move
psn-unified.js:1237 [PSN] Usando handSize dal server per white: 4 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
script.js:11231 [ID SYNC] ID corretto confermato: 6AYeFLxFhFdCYesqAAAV per username: bruscolino
script.js:11244 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11245 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11246 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11247 [HANDLE GAME STATE] - currentMode: online
script.js:11248 [HANDLE GAME STATE] - window.myPlayerId: 6AYeFLxFhFdCYesqAAAV
script.js:11306 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
script.js:11323 [GAME STATE] Stato ricevuto mentre processingDropId = 1751038928668_8ins5pdut
script.js:11327 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 2, Bianco - Paper 7 su c1. Prossimo giocatore: black. StateTurn: 2
psn-unified.js:1887 [PSN] Skip rilevazione pescate - registrazione dal server in corso
script.js:12138 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12141 [GAME STATE] player1 (white): Array(3)
script.js:12141 [GAME STATE] player2 (black): Array(4)
script.js:12361 [PERMANENT NAMES] Nomi permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"giggio","6AYeFLxFhFdCYesqAAAV":"bruscolino"}
script.js:12383 [PERMANENT COLORS] Colori permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"black","6AYeFLxFhFdCYesqAAAV":"white"}
script.js:12455 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12456 [ANIMATION DEBUG] - isStarting: false
script.js:12457 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12458 [ANIMATION DEBUG] - state.gameId: CJVJ6E
script.js:12459 [ANIMATION DEBUG] - state.gameOver: false
script.js:12460 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12461 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12462 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12463 [ANIMATION DEBUG] - state.mode: online
script.js:12464 [ANIMATION DEBUG] - window.animationsCompletedForGame: CJVJ6E
script.js:12465 [ANIMATION DEBUG] - needsAnimations: false
script.js:14414 [UPDATE UI] originalCurrentPlayerId già presente nello stato: pcqpMrFO2Hk1R8-IAAAL
script.js:13598 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13664 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13665 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13721 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14442 [DEBUG] Vantaggio calcolato: 50%
script.js:15887 [HISTORY] Aggiunta mossa #4 alla cronologia
script.js:15927 Tentativo di forzare rendering rating...
script.js:15954 Ratings dopo init: Object
script.js:15963 Aggiornamento avatar player1: player1
script.js:15969 Aggiornamento avatar player2: player2
script.js:14514 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14517 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14532 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: pcqpMrFO2Hk1R8-IAAAL
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:14540 [UPDATE UI] Chat visibility aggiornata per partita online
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:14657 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 3
script.js:4845 [OPPONENT HAND] player1-hand: previousSize=4, currentSize=3, hasPlayedCard=true
script.js:4849 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
script.js:4856 [OPPONENT HAND] Carta giocata dall'avversario: Paper-7
script.js:4904 [REMOVE OPPONENT CARD] Rimuovendo carta Paper-7 da player1-hand
script.js:4914 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
script.js:4867 [OPPONENT HAND] Carta dell'avversario Paper-7 rimossa permanentemente da player1-hand - NON verrà ricreata
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 4
script.js:4845 [OPPONENT HAND] player2-hand: previousSize=4, currentSize=4, hasPlayedCard=false
script.js:14909 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14910 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14912 [DEBUG] updateGameUI AFTER renderBoard
script.js:14913 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14914 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14934 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14935 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13087 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1531 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1532 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1533 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1563 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1564 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1565 [TIMER DEBUG] - isMyTurn: false
multiplayer.js:1566 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1569 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1575 [TIMER DEBUG] Non è il mio turno, fermo timer
multiplayer.js:1586 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1587 [TIMER DEBUG] - window: true
multiplayer.js:1588 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1589 [TIMER DEBUG] - player1TotalTime: 1794
multiplayer.js:1590 [TIMER DEBUG] - player2TotalTime: 1783
multiplayer.js:1591 [TIMER DEBUG] - gameState.currentPlayerId: player2
multiplayer.js:1603 [TIMER] Modalità online - currentPlayerId: player2
multiplayer.js:1604 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
multiplayer.js:1676 [TIMER] Avviato timer totale per giocatore 2
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10720 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2222 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2243 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2261 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2613 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: visible  
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
script.js:11231 [ID SYNC] ID corretto confermato: 6AYeFLxFhFdCYesqAAAV per username: bruscolino
script.js:11244 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11245 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11246 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11247 [HANDLE GAME STATE] - currentMode: online
script.js:11248 [HANDLE GAME STATE] - window.myPlayerId: 6AYeFLxFhFdCYesqAAAV
script.js:11306 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 2, Nero - Scissors 9 su b1. Prossimo giocatore: white. StateTurn: 2
psn-unified.js:1887 [PSN] Skip rilevazione pescate - registrazione dal server in corso
script.js:12138 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12141 [GAME STATE] player1 (white): Array(3)
script.js:12141 [GAME STATE] player2 (black): Array(3)
script.js:12361 [PERMANENT NAMES] Nomi permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"giggio","6AYeFLxFhFdCYesqAAAV":"bruscolino"}
script.js:12383 [PERMANENT COLORS] Colori permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"black","6AYeFLxFhFdCYesqAAAV":"white"}
script.js:12455 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12456 [ANIMATION DEBUG] - isStarting: false
script.js:12457 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12458 [ANIMATION DEBUG] - state.gameId: CJVJ6E
script.js:12459 [ANIMATION DEBUG] - state.gameOver: false
script.js:12460 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12461 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12462 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12463 [ANIMATION DEBUG] - state.mode: online
script.js:12464 [ANIMATION DEBUG] - window.animationsCompletedForGame: CJVJ6E
script.js:12465 [ANIMATION DEBUG] - needsAnimations: false
script.js:14414 [UPDATE UI] originalCurrentPlayerId già presente nello stato: 6AYeFLxFhFdCYesqAAAV
script.js:13598 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13664 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13665 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13721 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14442 [DEBUG] Vantaggio calcolato: 50%
script.js:15887 [HISTORY] Aggiunta mossa #5 alla cronologia
script.js:15927 Tentativo di forzare rendering rating...
script.js:15954 Ratings dopo init: Object
script.js:15963 Aggiornamento avatar player1: player1
script.js:15969 Aggiornamento avatar player2: player2
script.js:14514 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14517 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14532 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: 6AYeFLxFhFdCYesqAAAV
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:14540 [UPDATE UI] Chat visibility aggiornata per partita online
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:14657 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 3
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 3
script.js:4845 [OPPONENT HAND] player2-hand: previousSize=4, currentSize=3, hasPlayedCard=true
script.js:4849 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
script.js:4856 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-9
script.js:4904 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-9 da player2-hand
script.js:4914 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
script.js:4867 [OPPONENT HAND] Carta dell'avversario Scissors-9 rimossa permanentemente da player2-hand - NON verrà ricreata
script.js:14909 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14910 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:11755 [OPPONENT MARKER] Identificata ultima mossa avversario in b1
script.js:11844 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in b1
script.js:14912 [DEBUG] updateGameUI AFTER renderBoard
script.js:14913 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14914 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14934 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14935 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13087 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1531 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1532 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1533 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1563 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1564 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1565 [TIMER DEBUG] - isMyTurn: true
multiplayer.js:1566 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1569 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1571 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
multiplayer.js:1002 [TIMER] Avvio timer di turno: 60 secondi rimanenti
multiplayer.js:1586 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1587 [TIMER DEBUG] - window: true
multiplayer.js:1588 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1589 [TIMER DEBUG] - player1TotalTime: 1794
multiplayer.js:1590 [TIMER DEBUG] - player2TotalTime: 1778
multiplayer.js:1591 [TIMER DEBUG] - gameState.currentPlayerId: player1
multiplayer.js:1603 [TIMER] Modalità online - currentPlayerId: player1
multiplayer.js:1604 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
multiplayer.js:1617 [TIMER] Avviato timer totale per giocatore 1
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
psn-unified.js:1237 [PSN] Usando handSize dal server per white: 3 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
script.js:2389 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2392 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2418 [VISIBILITY] Animazione setup completata o in corso - NON riavvio per evitare scatto doppio
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: hidden  
script.js:1606 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10116 [DRAG START] Inizio trascinamento carta Paper-J del giocatore white
script.js:10117 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
script.js:10158 [DRAG ONLINE] È il mio turno? true
script.js:10164 [DRAG ONLINE] Il mio colore: white
script.js:10183 [DEBUG] effectiveTurnPlayerId: player1
script.js:10184 [DEBUG] turnPlayer extracted: Object
script.js:10247 Drag Start: Object
script.js:10248 Card suit: Paper Card value: J Type of value: string
script.js:10252 Current player hand: Array(3)
script.js:10258 Full hand data: [{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-J","suit":"Paper","value":"J"}]
script.js:10287 Card data being sent: Object
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10339 [DRAG END] DropEffect: none
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10116 [DRAG START] Inizio trascinamento carta Paper-8 del giocatore white
script.js:10117 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
script.js:10158 [DRAG ONLINE] È il mio turno? true
script.js:10164 [DRAG ONLINE] Il mio colore: white
script.js:10183 [DEBUG] effectiveTurnPlayerId: player1
script.js:10184 [DEBUG] turnPlayer extracted: Object
script.js:10247 Drag Start: Object
script.js:10248 Card suit: Paper Card value: 8 Type of value: string
script.js:10252 Current player hand: Array(3)
script.js:10258 Full hand data: [{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-J","suit":"Paper","value":"J"}]
script.js:10287 Card data being sent: Object
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:5314 [CELL DROP LISTENER] Evento drop catturato su cella: cell-d2 Event target: cell-d2
script.js:10391 [DROP SUCCESS] Carta rilasciata su cella: cell-d2 Drop ID: 1751038941042_jfgp9gnqf Timestamp: 2025-06-27T15:42:21.042Z
script.js:10418 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751038941042_jfgp9gnqf
script.js:10427 Dropped Card Data: Object
script.js:10445 [DROP ONLINE] È il mio turno? true
script.js:10451 [DROP ONLINE] Il mio colore: white
script.js:10470 [DROP DEBUG] effectiveTurnPlayerId: player1
script.js:10471 [DROP DEBUG] turnPlayer extracted: Object
script.js:10622 (white) tenta di piazzare [DRAG] 8 di Paper su d2
script.js:10640 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751038941042_jfgp9gnqf
script.js:10700 [DROP] Carta temporanea aggiunta alla cella: d2
script.js:978 [OPPONENT MARKER] Rimuovendo marker esistente
script.js:10706 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
script.js:10339 [DRAG END] DropEffect: move
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
script.js:11231 [ID SYNC] ID corretto confermato: 6AYeFLxFhFdCYesqAAAV per username: bruscolino
script.js:11244 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11245 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11246 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11247 [HANDLE GAME STATE] - currentMode: online
script.js:11248 [HANDLE GAME STATE] - window.myPlayerId: 6AYeFLxFhFdCYesqAAAV
script.js:11306 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
script.js:11323 [GAME STATE] Stato ricevuto mentre processingDropId = 1751038941042_jfgp9gnqf
script.js:11327 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 3, Bianco - Paper 8 su d2. Prossimo giocatore: black. StateTurn: 3
psn-unified.js:1887 [PSN] Skip rilevazione pescate - registrazione dal server in corso
script.js:12138 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12141 [GAME STATE] player1 (white): Array(2)
script.js:12141 [GAME STATE] player2 (black): Array(3)
script.js:12361 [PERMANENT NAMES] Nomi permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"giggio","6AYeFLxFhFdCYesqAAAV":"bruscolino"}
script.js:12383 [PERMANENT COLORS] Colori permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"black","6AYeFLxFhFdCYesqAAAV":"white"}
script.js:12455 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12456 [ANIMATION DEBUG] - isStarting: false
script.js:12457 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12458 [ANIMATION DEBUG] - state.gameId: CJVJ6E
script.js:12459 [ANIMATION DEBUG] - state.gameOver: false
script.js:12460 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12461 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12462 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12463 [ANIMATION DEBUG] - state.mode: online
script.js:12464 [ANIMATION DEBUG] - window.animationsCompletedForGame: CJVJ6E
script.js:12465 [ANIMATION DEBUG] - needsAnimations: false
script.js:14414 [UPDATE UI] originalCurrentPlayerId già presente nello stato: pcqpMrFO2Hk1R8-IAAAL
script.js:13598 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13664 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13665 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13721 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14442 [DEBUG] Vantaggio calcolato: 50%
script.js:15887 [HISTORY] Aggiunta mossa #6 alla cronologia
script.js:15927 Tentativo di forzare rendering rating...
script.js:15954 Ratings dopo init: Object
script.js:15963 Aggiornamento avatar player1: player1
script.js:15969 Aggiornamento avatar player2: player2
script.js:14514 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14517 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14532 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: pcqpMrFO2Hk1R8-IAAAL
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:14540 [UPDATE UI] Chat visibility aggiornata per partita online
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:14657 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 2
script.js:4845 [OPPONENT HAND] player1-hand: previousSize=3, currentSize=2, hasPlayedCard=true
script.js:4849 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
script.js:4856 [OPPONENT HAND] Carta giocata dall'avversario: Paper-8
script.js:4904 [REMOVE OPPONENT CARD] Rimuovendo carta Paper-8 da player1-hand
script.js:4914 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
script.js:4867 [OPPONENT HAND] Carta dell'avversario Paper-8 rimossa permanentemente da player1-hand - NON verrà ricreata
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 3
script.js:4845 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=3, hasPlayedCard=false
script.js:14909 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14910 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14912 [DEBUG] updateGameUI AFTER renderBoard
script.js:14913 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14914 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14934 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14935 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13087 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1531 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1532 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1533 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1563 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1564 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1565 [TIMER DEBUG] - isMyTurn: false
multiplayer.js:1566 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1569 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1575 [TIMER DEBUG] Non è il mio turno, fermo timer
multiplayer.js:1586 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1587 [TIMER DEBUG] - window: true
multiplayer.js:1588 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1589 [TIMER DEBUG] - player1TotalTime: 1788
multiplayer.js:1590 [TIMER DEBUG] - player2TotalTime: 1778
multiplayer.js:1591 [TIMER DEBUG] - gameState.currentPlayerId: player2
multiplayer.js:1603 [TIMER] Modalità online - currentPlayerId: player2
multiplayer.js:1604 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
multiplayer.js:1676 [TIMER] Avviato timer totale per giocatore 2
psn-unified.js:1237 [PSN] Usando handSize dal server per black: 3 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2222 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2243 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2261 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2613 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: visible  
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10720 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
script.js:11231 [ID SYNC] ID corretto confermato: 6AYeFLxFhFdCYesqAAAV per username: bruscolino
script.js:11244 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11245 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11246 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11247 [HANDLE GAME STATE] - currentMode: online
script.js:11248 [HANDLE GAME STATE] - window.myPlayerId: 6AYeFLxFhFdCYesqAAAV
script.js:11306 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 3, Nero - Rock A su b2. Prossimo giocatore: white. StateTurn: 3
script.js:12138 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12141 [GAME STATE] player1 (white): Array(2)
script.js:12141 [GAME STATE] player2 (black): Array(2)
script.js:12361 [PERMANENT NAMES] Nomi permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"giggio","6AYeFLxFhFdCYesqAAAV":"bruscolino"}
script.js:12383 [PERMANENT COLORS] Colori permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"black","6AYeFLxFhFdCYesqAAAV":"white"}
script.js:12455 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12456 [ANIMATION DEBUG] - isStarting: false
script.js:12457 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12458 [ANIMATION DEBUG] - state.gameId: CJVJ6E
script.js:12459 [ANIMATION DEBUG] - state.gameOver: false
script.js:12460 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12461 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12462 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12463 [ANIMATION DEBUG] - state.mode: online
script.js:12464 [ANIMATION DEBUG] - window.animationsCompletedForGame: CJVJ6E
script.js:12465 [ANIMATION DEBUG] - needsAnimations: false
script.js:14414 [UPDATE UI] originalCurrentPlayerId già presente nello stato: 6AYeFLxFhFdCYesqAAAV
script.js:13598 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13664 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13665 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13721 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14442 [DEBUG] Vantaggio calcolato: 50%
script.js:15887 [HISTORY] Aggiunta mossa #7 alla cronologia
script.js:15927 Tentativo di forzare rendering rating...
script.js:15954 Ratings dopo init: Object
script.js:15963 Aggiornamento avatar player1: player1
script.js:15969 Aggiornamento avatar player2: player2
script.js:14514 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14517 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14532 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: 6AYeFLxFhFdCYesqAAAV
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:14540 [UPDATE UI] Chat visibility aggiornata per partita online
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:14657 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 2
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 2
script.js:4845 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=2, hasPlayedCard=true
script.js:4849 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
script.js:4856 [OPPONENT HAND] Carta giocata dall'avversario: Rock-A
script.js:4904 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-A da player2-hand
script.js:4914 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
script.js:4867 [OPPONENT HAND] Carta dell'avversario Rock-A rimossa permanentemente da player2-hand - NON verrà ricreata
script.js:14909 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14910 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:11755 [OPPONENT MARKER] Identificata ultima mossa avversario in b2
script.js:11844 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in b2
script.js:14912 [DEBUG] updateGameUI AFTER renderBoard
script.js:14913 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14914 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14934 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14935 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13087 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1531 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1532 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1533 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1563 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1564 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1565 [TIMER DEBUG] - isMyTurn: true
multiplayer.js:1566 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1569 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1571 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
multiplayer.js:1002 [TIMER] Avvio timer di turno: 60 secondi rimanenti
multiplayer.js:1586 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1587 [TIMER DEBUG] - window: true
multiplayer.js:1588 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1589 [TIMER DEBUG] - player1TotalTime: 1788
multiplayer.js:1590 [TIMER DEBUG] - player2TotalTime: 1774
multiplayer.js:1591 [TIMER DEBUG] - gameState.currentPlayerId: player1
multiplayer.js:1603 [TIMER] Modalità online - currentPlayerId: player1
multiplayer.js:1604 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
multiplayer.js:1617 [TIMER] Avviato timer totale per giocatore 1
psn-unified.js:1237 [PSN] Usando handSize dal server per white: 2 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2389 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2392 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2418 [VISIBILITY] Animazione setup completata o in corso - NON riavvio per evitare scatto doppio
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: hidden  
script.js:1606 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10116 [DRAG START] Inizio trascinamento carta Paper-J del giocatore white
script.js:10117 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
script.js:10158 [DRAG ONLINE] È il mio turno? true
script.js:10164 [DRAG ONLINE] Il mio colore: white
script.js:10183 [DEBUG] effectiveTurnPlayerId: player1
script.js:10184 [DEBUG] turnPlayer extracted: Object
script.js:10247 Drag Start: Object
script.js:10248 Card suit: Paper Card value: J Type of value: string
script.js:10252 Current player hand: Array(2)
script.js:10258 Full hand data: [{"id":"Rock-Q","suit":"Rock","value":"Q"},{"id":"Paper-J","suit":"Paper","value":"J"}]
script.js:10287 Card data being sent: Object
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:5314 [CELL DROP LISTENER] Evento drop catturato su cella: cell-b3 Event target: cell-b3
script.js:10391 [DROP SUCCESS] Carta rilasciata su cella: cell-b3 Drop ID: 1751038953459_r16v16p31 Timestamp: 2025-06-27T15:42:33.459Z
script.js:10418 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751038953459_r16v16p31
script.js:10427 Dropped Card Data: Object
script.js:10445 [DROP ONLINE] È il mio turno? true
script.js:10451 [DROP ONLINE] Il mio colore: white
script.js:10470 [DROP DEBUG] effectiveTurnPlayerId: player1
script.js:10471 [DROP DEBUG] turnPlayer extracted: Object
script.js:10622 (white) tenta di piazzare [DRAG] J di Paper su b3
script.js:10640 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751038953459_r16v16p31
script.js:10700 [DROP] Carta temporanea aggiunta alla cella: b3
script.js:978 [OPPONENT MARKER] Rimuovendo marker esistente
script.js:10706 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
script.js:10339 [DRAG END] DropEffect: move
psn-unified.js:1237 [PSN] Usando handSize dal server per white: 2 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
script.js:11231 [ID SYNC] ID corretto confermato: 6AYeFLxFhFdCYesqAAAV per username: bruscolino
script.js:11244 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11245 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11246 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11247 [HANDLE GAME STATE] - currentMode: online
script.js:11248 [HANDLE GAME STATE] - window.myPlayerId: 6AYeFLxFhFdCYesqAAAV
script.js:11306 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
script.js:11323 [GAME STATE] Stato ricevuto mentre processingDropId = 1751038953459_r16v16p31
script.js:11327 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 4, Bianco - Paper J su b3. Prossimo giocatore: black. StateTurn: 4
psn-unified.js:1887 [PSN] Skip rilevazione pescate - registrazione dal server in corso
script.js:12138 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12141 [GAME STATE] player1 (white): Array(1)
script.js:12141 [GAME STATE] player2 (black): Array(2)
script.js:12361 [PERMANENT NAMES] Nomi permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"giggio","6AYeFLxFhFdCYesqAAAV":"bruscolino"}
script.js:12383 [PERMANENT COLORS] Colori permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"black","6AYeFLxFhFdCYesqAAAV":"white"}
script.js:12455 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12456 [ANIMATION DEBUG] - isStarting: false
script.js:12457 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12458 [ANIMATION DEBUG] - state.gameId: CJVJ6E
script.js:12459 [ANIMATION DEBUG] - state.gameOver: false
script.js:12460 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12461 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12462 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12463 [ANIMATION DEBUG] - state.mode: online
script.js:12464 [ANIMATION DEBUG] - window.animationsCompletedForGame: CJVJ6E
script.js:12465 [ANIMATION DEBUG] - needsAnimations: false
script.js:14414 [UPDATE UI] originalCurrentPlayerId già presente nello stato: pcqpMrFO2Hk1R8-IAAAL
script.js:13598 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13664 [ADVANTAGE] Componenti: Vertici (0-0), Carte (1-0), Adiacenze (0-0)
script.js:13665 [ADVANTAGE] Valore vantaggio calcolato: 1
script.js:13721 [ADVANTAGE] Percentuale finale: 56.7%
script.js:14442 [DEBUG] Vantaggio calcolato: 56.67%
script.js:15887 [HISTORY] Aggiunta mossa #8 alla cronologia
script.js:15927 Tentativo di forzare rendering rating...
script.js:15954 Ratings dopo init: Object
script.js:15963 Aggiornamento avatar player1: player1
script.js:15969 Aggiornamento avatar player2: player2
script.js:14514 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14517 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14532 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: pcqpMrFO2Hk1R8-IAAAL
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:14540 [UPDATE UI] Chat visibility aggiornata per partita online
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:14657 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:4845 [OPPONENT HAND] player1-hand: previousSize=2, currentSize=1, hasPlayedCard=true
script.js:4849 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
script.js:4856 [OPPONENT HAND] Carta giocata dall'avversario: Paper-J
script.js:4904 [REMOVE OPPONENT CARD] Rimuovendo carta Paper-J da player1-hand
script.js:4914 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
script.js:4867 [OPPONENT HAND] Carta dell'avversario Paper-J rimossa permanentemente da player1-hand - NON verrà ricreata
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 2
script.js:4845 [OPPONENT HAND] player2-hand: previousSize=2, currentSize=2, hasPlayedCard=false
script.js:14909 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14910 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14912 [DEBUG] updateGameUI AFTER renderBoard
script.js:14913 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14914 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14934 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14935 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13087 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1531 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1532 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1533 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1563 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1564 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1565 [TIMER DEBUG] - isMyTurn: false
multiplayer.js:1566 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1569 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1575 [TIMER DEBUG] Non è il mio turno, fermo timer
multiplayer.js:1586 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1587 [TIMER DEBUG] - window: true
multiplayer.js:1588 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1589 [TIMER DEBUG] - player1TotalTime: 1780
multiplayer.js:1590 [TIMER DEBUG] - player2TotalTime: 1774
multiplayer.js:1591 [TIMER DEBUG] - gameState.currentPlayerId: player2
multiplayer.js:1603 [TIMER] Modalità online - currentPlayerId: player2
multiplayer.js:1604 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
multiplayer.js:1676 [TIMER] Avviato timer totale per giocatore 2
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2222 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2243 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2261 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2613 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: visible  
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10720 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
script.js:11231 [ID SYNC] ID corretto confermato: 6AYeFLxFhFdCYesqAAAV per username: bruscolino
script.js:11244 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11245 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11246 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11247 [HANDLE GAME STATE] - currentMode: online
script.js:11248 [HANDLE GAME STATE] - window.myPlayerId: 6AYeFLxFhFdCYesqAAAV
script.js:11306 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 4, Nero - Rock 7 su a2. Prossimo giocatore: white. StateTurn: 4
psn-unified.js:1887 [PSN] Skip rilevazione pescate - registrazione dal server in corso
script.js:12138 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12141 [GAME STATE] player1 (white): Array(1)
script.js:12141 [GAME STATE] player2 (black): Array(1)
script.js:12361 [PERMANENT NAMES] Nomi permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"giggio","6AYeFLxFhFdCYesqAAAV":"bruscolino"}
script.js:12383 [PERMANENT COLORS] Colori permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"black","6AYeFLxFhFdCYesqAAAV":"white"}
script.js:12455 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12456 [ANIMATION DEBUG] - isStarting: false
script.js:12457 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12458 [ANIMATION DEBUG] - state.gameId: CJVJ6E
script.js:12459 [ANIMATION DEBUG] - state.gameOver: false
script.js:12460 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12461 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12462 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12463 [ANIMATION DEBUG] - state.mode: online
script.js:12464 [ANIMATION DEBUG] - window.animationsCompletedForGame: CJVJ6E
script.js:12465 [ANIMATION DEBUG] - needsAnimations: false
script.js:14414 [UPDATE UI] originalCurrentPlayerId già presente nello stato: 6AYeFLxFhFdCYesqAAAV
script.js:13598 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13664 [ADVANTAGE] Componenti: Vertici (0-0), Carte (1-1), Adiacenze (0-0)
script.js:13665 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13721 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14442 [DEBUG] Vantaggio calcolato: 50%
script.js:15887 [HISTORY] Aggiunta mossa #9 alla cronologia
script.js:15927 Tentativo di forzare rendering rating...
script.js:15954 Ratings dopo init: Object
script.js:15963 Aggiornamento avatar player1: player1
script.js:15969 Aggiornamento avatar player2: player2
script.js:14514 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14517 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14532 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: 6AYeFLxFhFdCYesqAAAV
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:14540 [UPDATE UI] Chat visibility aggiornata per partita online
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:14657 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:5033 [CARD DRAG] Rendendo trascinabile ULTIMA carta Rock-Q in player1-hand
script.js:5038 [CARD DRAG] Listener aggiunti per ULTIMA carta. Draggable: true, Cursor: grab
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:4845 [OPPONENT HAND] player2-hand: previousSize=2, currentSize=1, hasPlayedCard=true
script.js:4849 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
script.js:4856 [OPPONENT HAND] Carta giocata dall'avversario: Rock-7
script.js:4904 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-7 da player2-hand
script.js:4914 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
script.js:4867 [OPPONENT HAND] Carta dell'avversario Rock-7 rimossa permanentemente da player2-hand - NON verrà ricreata
script.js:14909 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14910 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:11755 [OPPONENT MARKER] Identificata ultima mossa avversario in a2
script.js:11844 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in a2
script.js:14912 [DEBUG] updateGameUI AFTER renderBoard
script.js:14913 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14914 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14934 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14935 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13087 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1531 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1532 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1533 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1563 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1564 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1565 [TIMER DEBUG] - isMyTurn: true
multiplayer.js:1566 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1569 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1571 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
multiplayer.js:1002 [TIMER] Avvio timer di turno: 60 secondi rimanenti
multiplayer.js:1586 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1587 [TIMER DEBUG] - window: true
multiplayer.js:1588 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1589 [TIMER DEBUG] - player1TotalTime: 1780
multiplayer.js:1590 [TIMER DEBUG] - player2TotalTime: 1768
multiplayer.js:1591 [TIMER DEBUG] - gameState.currentPlayerId: player1
multiplayer.js:1603 [TIMER] Modalità online - currentPlayerId: player1
multiplayer.js:1604 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
multiplayer.js:1617 [TIMER] Avviato timer totale per giocatore 1
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
psn-unified.js:1237 [PSN] Usando handSize dal server per white: 1 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2389 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2392 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2418 [VISIBILITY] Animazione setup completata o in corso - NON riavvio per evitare scatto doppio
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: hidden  
script.js:1606 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10116 [DRAG START] Inizio trascinamento carta Rock-Q del giocatore white
script.js:10117 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
script.js:10158 [DRAG ONLINE] È il mio turno? true
script.js:10164 [DRAG ONLINE] Il mio colore: white
script.js:10183 [DEBUG] effectiveTurnPlayerId: player1
script.js:10184 [DEBUG] turnPlayer extracted: Object
script.js:10247 Drag Start: Object
script.js:10248 Card suit: Rock Card value: Q Type of value: string
script.js:10252 Current player hand: Array(1)
script.js:10258 Full hand data: [{"id":"Rock-Q","suit":"Rock","value":"Q"}]
script.js:10287 Card data being sent: Object
script.js:5314 [CELL DROP LISTENER] Evento drop catturato su cella: cell-a1 Event target: cell-a1
script.js:10391 [DROP SUCCESS] Carta rilasciata su cella: cell-a1 Drop ID: 1751038968926_o6qeyarpt Timestamp: 2025-06-27T15:42:48.926Z
script.js:10418 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751038968926_o6qeyarpt
script.js:10427 Dropped Card Data: Object
script.js:10445 [DROP ONLINE] È il mio turno? true
script.js:10451 [DROP ONLINE] Il mio colore: white
script.js:10470 [DROP DEBUG] effectiveTurnPlayerId: player1
script.js:10471 [DROP DEBUG] turnPlayer extracted: Object
script.js:10622 (white) tenta di piazzare [DRAG] Q di Rock su a1
script.js:10640 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751038968926_o6qeyarpt
script.js:10700 [DROP] Carta temporanea aggiunta alla cella: a1
script.js:978 [OPPONENT MARKER] Rimuovendo marker esistente
script.js:10706 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
script.js:10339 [DRAG END] DropEffect: move
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: Object
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
script.js:11231 [ID SYNC] ID corretto confermato: 6AYeFLxFhFdCYesqAAAV per username: bruscolino
script.js:11244 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11245 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11246 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11247 [HANDLE GAME STATE] - currentMode: online
script.js:11248 [HANDLE GAME STATE] - window.myPlayerId: 6AYeFLxFhFdCYesqAAAV
script.js:11306 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
script.js:11323 [GAME STATE] Stato ricevuto mentre processingDropId = 1751038968926_o6qeyarpt
script.js:11327 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 5, Bianco - Rock Q su a1 (controllo vertice ottenuto). Prossimo giocatore: black. StateTurn: 5
psn-unified.js:1887 [PSN] Skip rilevazione pescate - registrazione dal server in corso
script.js:12138 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12141 [GAME STATE] player1 (white): Array(0)
script.js:12141 [GAME STATE] player2 (black): Array(1)
script.js:12361 [PERMANENT NAMES] Nomi permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"giggio","6AYeFLxFhFdCYesqAAAV":"bruscolino"}
script.js:12383 [PERMANENT COLORS] Colori permanenti finali: {"pcqpMrFO2Hk1R8-IAAAL":"black","6AYeFLxFhFdCYesqAAAV":"white"}
script.js:12455 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12456 [ANIMATION DEBUG] - isStarting: false
script.js:12457 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12458 [ANIMATION DEBUG] - state.gameId: CJVJ6E
script.js:12459 [ANIMATION DEBUG] - state.gameOver: false
script.js:12460 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12461 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12462 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12463 [ANIMATION DEBUG] - state.mode: online
script.js:12464 [ANIMATION DEBUG] - window.animationsCompletedForGame: CJVJ6E
script.js:12465 [ANIMATION DEBUG] - needsAnimations: false
script.js:14414 [UPDATE UI] originalCurrentPlayerId già presente nello stato: pcqpMrFO2Hk1R8-IAAAL
script.js:13598 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13664 [ADVANTAGE] Componenti: Vertici (0-0), Carte (1-1), Adiacenze (0-0)
script.js:13665 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13721 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14442 [DEBUG] Vantaggio calcolato: 50%
script.js:15887 [HISTORY] Aggiunta mossa #10 alla cronologia
script.js:15927 Tentativo di forzare rendering rating...
script.js:15954 Ratings dopo init: Object
script.js:15963 Aggiornamento avatar player1: player1
script.js:15969 Aggiornamento avatar player2: player2
script.js:14514 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14517 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14532 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: pcqpMrFO2Hk1R8-IAAAL
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:14540 [UPDATE UI] Chat visibility aggiornata per partita online
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:14657 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 0
player-names-protection.js:289 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: bruscolino
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: giggio
player-names-protection.js:123 [NAMES PROTECTION] Protezione attivata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: bruscolino
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: giggio
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:4845 [OPPONENT HAND] player2-hand: previousSize=1, currentSize=1, hasPlayedCard=false
script.js:14909 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14910 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14912 [DEBUG] updateGameUI AFTER renderBoard
script.js:14913 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14914 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14934 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14935 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:13087 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1531 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1532 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1533 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1563 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1564 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1565 [TIMER DEBUG] - isMyTurn: false
multiplayer.js:1566 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1569 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1575 [TIMER DEBUG] Non è il mio turno, fermo timer
multiplayer.js:1586 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1587 [TIMER DEBUG] - window: true
multiplayer.js:1588 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1589 [TIMER DEBUG] - player1TotalTime: 1772
multiplayer.js:1590 [TIMER DEBUG] - player2TotalTime: 1768
multiplayer.js:1591 [TIMER DEBUG] - gameState.currentPlayerId: player2
multiplayer.js:1603 [TIMER] Modalità online - currentPlayerId: player2
multiplayer.js:1604 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
multiplayer.js:1676 [TIMER] Avviato timer totale per giocatore 2
psn-unified.js:1237 [PSN] Usando handSize dal server per black: 1 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:10720 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2222 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2243 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2261 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2613 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:2389 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2392 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2418 [VISIBILITY] Animazione setup completata o in corso - NON riavvio per evitare scatto doppio
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: {gameId: 'CJVJ6E', mode: 'online', players: {…}, board: {…}, discardPile: Array(0), …}
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2222 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2243 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2261 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2613 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: visible  
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1600 [SOCKET] Cambio di visibilità del client pcqpMrFO2Hk1R8-IAAAL: hidden  
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:2389 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2392 [VISIBILITY] Eseguo pulizia cover cards bloccate...
script.js:2418 [VISIBILITY] Animazione setup completata o in corso - NON riavvio per evitare scatto doppio
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
script.js:1204 [SOCKET] Ricevuto evento gameState: {gameId: 'CJVJ6E', mode: 'online', players: {…}, board: {…}, discardPile: Array(0), …}
script.js:1208 [SOCKET] Chiamando handleGameStateEvent
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2487 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: false, gameMode: 'online', gameId: 'CJVJ6E', isMultiplayerActive: 'CJVJ6E'}
multiplayer.js:2498 [CHAT] Chat abilitata per il multiplayer
