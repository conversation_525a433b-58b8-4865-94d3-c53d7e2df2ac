/**
 * online-game.js
 * Gestione specifica per partite online (multiplayer)
 * 
 * DETTAGLI DEL FUNZIONAMENTO:
 * 
 * 1. SCOPO:
 *    - Gestisce la logica specifica delle partite online
 *    - Mappa i giocatori remoti ai giocatori locali (white/black)
 *    - Sincronizza lo stato di gioco tra client e server
 *    - Gestisce la comunicazione delle mosse al server
 * 
 * 2. INIZIALIZZAZIONE:
 *    - Viene istanziato da game-mode-manager.js quando si avvia una partita online
 *    - Riceve socket e playerId dal GameModeManager
 *    - Si registra per ricevere aggiornamenti di stato dal server
 * 
 * 3. GESTIONE STATO:
 *    - updateGameState(): riceve nuovo stato dal server
 *    - Mappa gli ID dei giocatori remoti ai colori locali
 *    - Determina quale giocatore è l'avversario
 *    - Sincronizza le mani dei giocatori
 *    - Gestisce il mapping delle mosse
 * 
 * 4. COMUNICAZIONE:
 *    - sendCardPlacement(): invia posizionamento carta al server
 *    - handleOpponentMove(): processa mosse dell'avversario
 *    - Gestisce eventi di disconnessione/riconnessione
 * 
 * 5. INTEGRAZIONE:
 *    - Lavora con multiplayer.js per la comunicazione socket
 *    - Coordinato da game-mode-manager.js
 *    - Usa script.js per l'aggiornamento dell'UI
 */

class OnlineGameManager {
    constructor() {
        this.gameState = null;
        this.socket = null;
        this.myPlayerId = null;
        this.opponentId = null;
        this.gameId = null;
    }

    /**
     * Inizializza una partita online
     * @param {Socket} socket - Socket.io instance
     * @param {string} myPlayerId - ID del giocatore locale
     */
    initialize(socket, myPlayerId) {
        this.socket = socket;
        this.myPlayerId = myPlayerId;
        
        // [ONLINE GAME] Inizializzazione partita online
        
        // Crea gli slot per le carte nelle aree delle mani per la modalità online
        if (typeof window.createHandSlots === 'function') {
            // console.log('[ONLINE GAME] Creazione slot per le carte nelle aree delle mani');
            window.createHandSlots();
        } else {
            console.warn('[ONLINE GAME] Funzione createHandSlots non disponibile');
        }
    }

    /**
     * Gestisce l'aggiornamento dello stato di gioco
     * @param {Object} state - Nuovo stato di gioco dal server
     */
    updateGameState(state) {
        // [ONLINE GAME] Aggiornamento stato di gioco
        
        this.gameState = state;
        this.gameId = state.gameId;
        
        // Gestisci mapping dei giocatori per online game
        this.mapOnlinePlayers(state);
        
        // [ONLINE GAME] Mapping completato
        
        return state;
    }

    /**
     * Mappa i giocatori online basandosi sul colore
     * @param {Object} state - Stato di gioco
     */
    mapOnlinePlayers(state) {
        if (!state.players || !this.myPlayerId) return;
        
        // console.log('[ONLINE GAME MAPPING] === INIZIO MAPPING DEBUG ===');
        // console.log('[ONLINE GAME MAPPING] state.players keys:', Object.keys(state.players));
        // console.log('[ONLINE GAME MAPPING] this.myPlayerId:', this.myPlayerId);
        // console.log('[ONLINE GAME MAPPING] state.currentPlayerId:', state.currentPlayerId);
        // console.log('[ONLINE GAME MAPPING] state.originalCurrentPlayerId (prima):', state.originalCurrentPlayerId);
        
        // CORREZIONE: Controlla se il mapping è già stato fatto
        if (state.players.player1 && state.players.player2) {
            // console.log('[ONLINE GAME MAPPING] Mapping già fatto, skip');
            // console.log('[ONLINE GAME MAPPING] player1.id:', state.players.player1.id, 'color:', state.players.player1.color);
            // console.log('[ONLINE GAME MAPPING] player2.id:', state.players.player2.id, 'color:', state.players.player2.color);
            // console.log('[ONLINE GAME MAPPING] Controllo originalCurrentPlayerId...');
            // console.log('[ONLINE GAME MAPPING] state.originalCurrentPlayerId PRIMA:', state.originalCurrentPlayerId);
            // console.log('[ONLINE GAME MAPPING] state.currentPlayerId:', state.currentPlayerId);
            
            // Assicurati che originalCurrentPlayerId sia preservato
            if (!state.originalCurrentPlayerId && state.currentPlayerId !== 'player1' && state.currentPlayerId !== 'player2') {
                state.originalCurrentPlayerId = state.currentPlayerId;
                // console.log('[ONLINE GAME MAPPING] Preservato originalCurrentPlayerId tardivo:', state.originalCurrentPlayerId);
            }
            
            // NUOVA LOGICA MIGLIORATA: Se originalCurrentPlayerId manca ma abbiamo i dati necessari, ricostruiscilo
            if (!state.originalCurrentPlayerId && (state.currentPlayerId === 'player1' || state.currentPlayerId === 'player2')) {
                // console.log('[ONLINE GAME MAPPING] originalCurrentPlayerId manca, tento ricostruzione...');
                // console.log('[ONLINE GAME MAPPING] state.currentPlayerId:', state.currentPlayerId);
                
                // Trova chi è il giocatore corrente basandosi su currentPlayerId
                let currentPlayerSocketId = null;
                
                if (state.currentPlayerId === 'player1') {
                    // Il turno è di player1 (bianco)
                    currentPlayerSocketId = state.players.player1.id;
                    // console.log('[ONLINE GAME MAPPING] Turno di player1 (bianco), socket ID:', currentPlayerSocketId);
                } else if (state.currentPlayerId === 'player2') {
                    // Il turno è di player2 (nero)
                    currentPlayerSocketId = state.players.player2.id;
                    // console.log('[ONLINE GAME MAPPING] Turno di player2 (nero), socket ID:', currentPlayerSocketId);
                }
                
                if (currentPlayerSocketId) {
                    state.originalCurrentPlayerId = currentPlayerSocketId;
                    // console.log('[ONLINE GAME MAPPING] ✅ Ricostruito originalCurrentPlayerId:', state.originalCurrentPlayerId);
                } else {
                    // console.log('[ONLINE GAME MAPPING] ❌ Impossibile ricostruire originalCurrentPlayerId');
                }
            }
            
            // console.log('[ONLINE GAME MAPPING] originalCurrentPlayerId finale:', state.originalCurrentPlayerId);
            // console.log('[ONLINE GAME MAPPING] === FINE MAPPING SKIP ===');
            return;
        }
        
        // Trova l'ID dell'avversario dai dati originali
        this.opponentId = Object.keys(state.players).find(id => id !== this.myPlayerId);
        
        const localPlayerData = state.players[this.myPlayerId];
        const opponentPlayerData = state.players[this.opponentId];
        
        // console.log('[ONLINE GAME MAPPING] === DEBUG MAPPING ===');
        // console.log('[ONLINE GAME MAPPING] this.myPlayerId:', this.myPlayerId);
        // console.log('[ONLINE GAME MAPPING] this.opponentId:', this.opponentId);
        // console.log('[ONLINE GAME MAPPING] state.currentPlayerId:', state.currentPlayerId);
        // console.log('[ONLINE GAME MAPPING] localPlayerData:', localPlayerData);
        // console.log('[ONLINE GAME MAPPING] opponentPlayerData:', opponentPlayerData);
        
        // Assegna i dati in base al colore (bianco = player1, nero = player2)
        if (localPlayerData && opponentPlayerData) {
            // SALVA i socket ID originali prima della trasformazione per uso nelle mosse
            state.originalCurrentPlayerId = state.currentPlayerId;
            state.mySocketId = this.myPlayerId;
            state.opponentSocketId = this.opponentId;
            
            // Salva i giocatori originali per riferimento con DEEP COPY per evitare riferimenti condivisi
            const originalPlayers = JSON.parse(JSON.stringify(state.players));

            // Crea la struttura players con player1/player2 per compatibilità con animazioni
            // IMPORTANTE: Sostituisci completamente l'oggetto players per evitare duplicati
            state.players = {};

            // CORREZIONE CARTE DUPLICATE: Crea copie profonde per evitare riferimenti condivisi alle mani
            const localPlayerCopy = JSON.parse(JSON.stringify(localPlayerData));
            const opponentPlayerCopy = JSON.parse(JSON.stringify(opponentPlayerData));

            if (localPlayerData.color === 'white') {
                // console.log('[ONLINE GAME MAPPING] Io sono BIANCO, assegno: player1=me, player2=opponent');
                state.players.player1 = localPlayerCopy;
                state.players.player2 = opponentPlayerCopy;
                state.player1Data = localPlayerCopy;
                state.player2Data = opponentPlayerCopy;
            } else {
                // console.log('[ONLINE GAME MAPPING] Io sono NERO, assegno: player1=opponent, player2=me');
                state.players.player1 = opponentPlayerCopy;
                state.players.player2 = localPlayerCopy;
                state.player1Data = opponentPlayerCopy;
                state.player2Data = localPlayerCopy;
            }

            // Mantieni i giocatori originali per riferimento se necessario
            state.originalPlayers = originalPlayers;

            // DEBUG: Verifica che le mani siano diverse
                    // console.log('[ONLINE GAME MAPPING] 🔍 Verifica mani dopo mapping:');
        // console.log('[ONLINE GAME MAPPING] 🔍 player1.hand:', state.players.player1.hand?.map(c => c.id));
        // console.log('[ONLINE GAME MAPPING] 🔍 player2.hand:', state.players.player2.hand?.map(c => c.id));
            
            // DEBUG della trasformazione currentPlayerId
                    // console.log('[ONLINE GAME MAPPING] PRIMA trasformazione currentPlayerId:', state.currentPlayerId);
        // console.log('[ONLINE GAME MAPPING] Controllo: currentPlayerId === myPlayerId?', state.currentPlayerId === this.myPlayerId);
        // console.log('[ONLINE GAME MAPPING] Controllo: currentPlayerId === opponentId?', state.currentPlayerId === this.opponentId);
            
            // Trasforma anche currentPlayerId per essere compatibile con player1/player2
            if (state.currentPlayerId === this.myPlayerId) {
                const newCurrentPlayer = localPlayerData.color === 'white' ? 'player1' : 'player2';
                // console.log('[ONLINE GAME MAPPING] È il MIO turno, mio colore:', localPlayerData.color, '-> assegno currentPlayerId =', newCurrentPlayer);
                state.currentPlayerId = newCurrentPlayer;
            } else if (state.currentPlayerId === this.opponentId) {
                const newCurrentPlayer = opponentPlayerData.color === 'white' ? 'player1' : 'player2';
                // console.log('[ONLINE GAME MAPPING] È il turno dell\'AVVERSARIO, suo colore:', opponentPlayerData.color, '-> assegno currentPlayerId =', newCurrentPlayer);
                state.currentPlayerId = newCurrentPlayer;
            }
            
            // console.log('[ONLINE GAME MAPPING] DOPO trasformazione currentPlayerId:', state.currentPlayerId);
            
            // Assicurati che window.myPlayerId sia impostato correttamente per handleGameState
            if (!window.myPlayerId) {
                window.myPlayerId = this.myPlayerId;
                // console.log('[ONLINE GAME] Impostato window.myPlayerId =', window.myPlayerId);
            }
            
            // Mapping giocatori online completato
            // console.log('[ONLINE GAME MAPPING] === FINE DEBUG MAPPING ===');
        }
    }

    /**
     * Gestisce il posizionamento di una carta
     * @param {Object} card - Carta da posizionare
     * @param {string} position - Posizione sul tabellone
     */
    placeCard(card, position) {
        if (!this.socket || !this.gameId) return;
        
        // console.log('[ONLINE GAME] Posizionamento carta:', card, 'in', position);
        
        // Per online game, usa gameAction
        this.socket.emit('gameAction', {
            action: 'placeCard',
            gameId: this.gameId,
            playerId: window.myPlayerId || this.socket.id, // Invia il playerId persistente
            actionData: {
                card: card,
                position: position
            }
        });
    }

    /**
     * Gestisce la pesca di una carta
     */
    drawCard() {
        // console.log('[ONLINE GAME] === DEBUG DRAW CARD ===');
        // console.log('[ONLINE GAME] this.socket:', !!this.socket);
        // console.log('[ONLINE GAME] this.gameId:', this.gameId);
        // console.log('[ONLINE GAME] window.myPlayerId:', window.myPlayerId);
        // console.log('[ONLINE GAME] this.socket.id:', this.socket?.id);
        
        if (!this.socket) {
            console.error('[ONLINE GAME] ERRORE: socket non disponibile');
            return;
        }
        
        if (!this.gameId) {
            console.error('[ONLINE GAME] ERRORE: gameId non disponibile');
            return;
        }
        
        // console.log('[ONLINE GAME] Pesca carta - invio gameAction al server...');
        
        // Per online game, usa gameAction
        this.socket.emit('gameAction', {
            action: 'drawCard',
            gameId: this.gameId,
            playerId: window.myPlayerId || this.socket.id, // Invia il playerId persistente
            actionData: {}
        });
        
        // console.log('[ONLINE GAME] gameAction inviato per drawCard');
    }

    /**
     * Gestisce l'abbandono della partita
     */
    leaveGame() {
        if (!this.socket || !this.gameId) return;
        
        // console.log('[ONLINE GAME] Abbandono partita');
        
        this.socket.emit('leaveGame', {
            gameId: this.gameId
        });
        
        this.reset();
    }

    /**
     * Reset completo del manager
     */
    reset() {
        this.gameState = null;
        this.gameId = null;
        this.opponentId = null;
        // console.log('[ONLINE GAME] Reset stato di gioco');
    }

    /**
     * Verifica se è il mio turno
     * @returns {boolean}
     */
    isMyTurn() {
        if (!this.gameState) return false;
        
        // Usa il socket ID originale per il controllo del turno
        const originalCurrentPlayerId = this.gameState.originalCurrentPlayerId;
        if (originalCurrentPlayerId) {
            return originalCurrentPlayerId === this.myPlayerId;
        }
        
        // Fallback per la logica precedente
        return this.gameState.currentPlayerId === this.myPlayerId;
    }

    /**
     * Ottiene i dati del giocatore locale
     * @returns {Object|null}
     */
    getMyPlayerData() {
        if (!this.gameState || !this.gameState.players) return null;
        
        return this.gameState.players[this.myPlayerId];
    }

    /**
     * Ottiene i dati dell'avversario
     * @returns {Object|null}
     */
    getOpponentData() {
        if (!this.gameState || !this.gameState.players || !this.opponentId) return null;
        
        return this.gameState.players[this.opponentId];
    }
}

// Crea un'istanza globale del manager
window.onlineGameManager = new OnlineGameManager();

// console.log('[ONLINE GAME] Manager caricato e pronto');