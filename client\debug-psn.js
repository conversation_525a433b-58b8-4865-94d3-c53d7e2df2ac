/**
 * Debug PSN - Script per il debug e risoluzione problemi con la notazione PSN
 * Aggiunge un pulsante per il debug della notazione PSN e abilita log estesi
 */

// Attendere il caricamento completo della pagina
document.addEventListener('DOMContentLoaded', function() {
    // console.log('[DEBUG-PSN] Caricamento script debug PSN...');
    
    // Aggiunge pulsante di debug alla pagina
    setTimeout(function() {
        // Cerca il container del gioco
        const gameMessageContainer = document.getElementById('game-message-container');
        if (gameMessageContainer) {
            // Crea un pulsante di debug
            const debugButton = document.createElement('button');
            debugButton.id = 'debug-psn-button';
            debugButton.className = 'action-button debug-button';
            debugButton.innerHTML = '<i class="fas fa-bug"></i><span>Debug PSN</span>';
            debugButton.style.backgroundColor = '#e74c3c';
            debugButton.style.color = '#fff';
            
            // Aggiunge evento click
            debugButton.addEventListener('click', debugPSN);
            
            // Aggiunge il pulsante al contenitore
            const gameMessageElement = document.getElementById('game-message');
            if (gameMessageElement) {
                gameMessageElement.parentNode.insertBefore(debugButton, gameMessageElement.nextSibling);
                // console.log('[DEBUG-PSN] Pulsante debug PSN aggiunto');
            }
        }
    }, 2000);  // Ritardo di 2 secondi per garantire che altri elementi siano caricati
    
    // Funzione di debug principale
    function debugPSN() {
        // console.log('[DEBUG-PSN] Esecuzione debug PSN...');

        // Visualizza lo stato corrente
        // console.log('[DEBUG-PSN] Stato corrente:');
        // console.log('- window.psnMoves esiste:', !!window.psnMoves);
        // console.log('- window.enhancedPSN esiste:', !!window.enhancedPSN);
        // console.log('- window.psn_moves esiste:', !!window.psn_moves);
        // console.log('- window.gameState esiste:', !!window.gameState);
        
        // Verifica contenuto PSN
        if (window.psnMoves && typeof window.psnMoves.getPSNContent === 'function') {
            const psnContent = window.psnMoves.getPSNContent();
            // console.log('[DEBUG-PSN] Contenuto PSN da psnMoves:', psnContent);
            
            // Verifica moveHistory
            if (window.psnMoves.moveHistory) {
                // console.log('[DEBUG-PSN] moveHistory:', window.psnMoves.moveHistory);
            }
        }
        
        if (window.enhancedPSN && typeof window.enhancedPSN.getPSNContent === 'function') {
            const enhancedContent = window.enhancedPSN.getPSNContent();
            // console.log('[DEBUG-PSN] Contenuto PSN da enhancedPSN:', enhancedContent);
        }
        
        if (window.psn_moves) {
            // console.log('[DEBUG-PSN] Contenuto variabile globale psn_moves:', window.psn_moves);
        }
        
        // Verifica elemento di visualizzazione
        const psnContentElement = document.getElementById('psn-content');
        if (psnContentElement) {
            // console.log('[DEBUG-PSN] Elemento psn-content trovato, contenuto HTML:', psnContentElement.innerHTML);
        } else {
            // console.log('[DEBUG-PSN] Elemento psn-content NON trovato');
        }
        
        // Genera nuova notazione di esempio
        const examplePSN = `0.e5|C6/CJ/F7/F8/P7:white;C8/F10/F4/FQ/P4:black
1.F8|d4|4;F4|c3|4
2.CJ|b2|3;F10|f3|3`;
        
        // console.log('[DEBUG-PSN] Creazione notazione di esempio:', examplePSN);
        
        // Forza aggiornamento
        if (window.psnMoves) {
            try {
                // Aggiorna direttamente il contenuto PSN
                window.psnMoves.psnContent = examplePSN;
                window.psn_moves = examplePSN;
                
                // Forza aggiornamento visualizzazione
                window.psnMoves._updateMoveDisplay();
                
                // Notifica altri sistemi
                if (window.enhancedPSN && typeof window.enhancedPSN.updateFromExternalSource === 'function') {
                    window.enhancedPSN.updateFromExternalSource(examplePSN);
                }
                
                // Aggiorna visualizzatore
                if (window.updatePSNVisualizer) {
                    window.updatePSNVisualizer();
                }
                
                // console.log('[DEBUG-PSN] Forzato aggiornamento con notazione di esempio');
            } catch (error) {
                console.error('[DEBUG-PSN] Errore durante l\'aggiornamento forzato:', error);
            }
        }
        
        // Verifica gli intercettori
        // console.log('[DEBUG-PSN] Verifica intercettori funzioni:');
        // console.log('- originalPlaceCardOnBoard esiste:', typeof window.originalPlaceCardOnBoard === 'function');
        // console.log('- placeCardOnBoard è stato intercettato:', window.placeCardOnBoard && window.placeCardOnBoard.toString().includes('[PSN Moves]'));
        // console.log('- drawCardFromDeck è stato intercettato:', window.drawCardFromDeck && window.drawCardFromDeck.toString().includes('[PSN Moves]'));
        
        // Crea e dispara un evento per testare gli event listener
        const testEvent = new CustomEvent('cardPlayed', {
            detail: {
                card: { suit: 'Rock', value: '8', id: 'Rock-8' },
                position: 'a2',
                playerId: window.gameState?.currentPlayerId || 'test-player',
                gainedControl: false
            }
        });
        
        // console.log('[DEBUG-PSN] Test con evento cardPlayed simulato');
        document.dispatchEvent(testEvent);
        
        // Mostra un alert per notificare l'utente
        alert('Debug PSN completato! Controlla la console per i dettagli.');
    }
    
    // Aggiungi un hook per catturare gli errori
    const originalUpdatePSN = window.psnMoves && window.psnMoves.updatePSN;
    if (originalUpdatePSN) {
        window.psnMoves.updatePSN = function() {
            try {
                // console.log('[DEBUG-PSN] Chiamata a updatePSN con argomenti:', arguments);
                return originalUpdatePSN.apply(this, arguments);
            } catch (error) {
                // console.error('[DEBUG-PSN] Errore in updatePSN:', error);
                return '';
            }
        };
    }
});