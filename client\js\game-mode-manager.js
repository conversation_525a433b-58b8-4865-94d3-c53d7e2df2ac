/**
 * game-mode-manager.js
 * Manager principale che coordina tra modalità locale e online
 * 
 * DETTAGLI DEL FUNZIONAMENTO:
 * 
 * 1. SCOPO PRINCIPALE:
 *    - Punto centrale di coordinamento per tutte le modalità di gioco
 *    - Determina se una partita è locale o online
 *    - Instrada gli eventi al manager appropriato (LocalGameManager o OnlineGameManager)
 *    - Mantiene lo stato corrente della modalità di gioco
 * 
 * 2. INIZIALIZZAZIONE:
 *    - Viene creato come istanza globale window.gameModeManager
 *    - initialize(): riceve il socket per la comunicazione
 *    - Verifica che LocalGameManager e OnlineGameManager siano disponibili
 * 
 * 3. AVVIO PARTITE:
 *    - startLocalGame(): inizia una partita locale
 *    - startOnlineGame(): inizia una partita online
 *    - Imposta currentMode e currentManager appropriati
 *    - Delega l'inizializzazione al manager specifico
 * 
 * 4. GESTIONE STATO:
 *    - updateGameState(): riceve aggiornamenti di stato
 *    - Determina automaticamente la modalità se non impostata
 *    - Instrada gli aggiornamenti al manager corretto
 *    - Gestisce transizioni tra modalità
 * 
 * 5. EVENTI E AZIONI:
 *    - handleCardPlacement(): gestisce posizionamento carte
 *    - handleDrawCard(): gestisce pescaggio carte
 *    - isOnlineMode(): verifica se in modalità online
 *    - getOpponentId(): ottiene ID avversario online
 * 
 * 6. INTEGRAZIONE:
 *    - Chiamato da script.js quando si riceve gameState
 *    - Utilizzato da multiplayer.js per inizializzare partite online
 *    - Coordina con online-game.js e local-game.js
 */

class GameModeManager {
    constructor() {
        this.currentMode = null;
        this.socket = null;
        this.currentManager = null;
    }

    /**
     * Inizializza il manager con il socket
     * @param {Socket} socket - Socket.io instance
     */
    initialize(socket) {
        this.socket = socket;
        
        // Assicurati che i manager siano disponibili
        if (!window.localGameManager || !window.onlineGameManager) {
            console.error('[GAME MODE] Manager locali o online non disponibili');
            return;
        }
        
        // console.log('[GAME MODE] Manager inizializzato');
    }

    /**
     * Avvia una partita locale
     * @param {string} player1Name - Nome del giocatore 1
     * @param {string} player2Name - Nome del giocatore 2
     */
    startLocalGame(player1Name, player2Name) {
        // console.log('[GAME MODE] Avvio partita locale');
        
        this.currentMode = 'local';
        this.currentManager = window.localGameManager;
        
        // Inizializza e avvia la partita locale
        this.currentManager.initialize(this.socket, player1Name, player2Name);
        this.currentManager.startGame();
    }

    /**
     * Avvia una partita online
     * @param {string} myPlayerId - ID del giocatore locale
     */
    startOnlineGame(myPlayerId) {
        // console.log('[GAME MODE] Avvio partita online');
        
        this.currentMode = 'online';
        this.currentManager = window.onlineGameManager;
        
        // Inizializza la partita online
        this.currentManager.initialize(this.socket, myPlayerId);
    }

    /**
     * Gestisce l'aggiornamento dello stato di gioco
     * @param {Object} state - Nuovo stato di gioco dal server
     */
    updateGameState(state) {
        if (!this.currentManager) {
            // In modalità online, spesso il manager non è ancora inizializzato ma il gameState arriva
            // In questo caso, processiamo lo stato direttamente senza errori
            if (state.mode === 'online') {
                // console.log('[GAME MODE] Processando stato online senza manager attivo (normale durante inizializzazione)');
                return state;
            } else {
                console.error('[GAME MODE] Nessun manager attivo per modalità:', state.mode);
                return state;
            }
        }
        
        // Delega l'aggiornamento al manager appropriato
        return this.currentManager.updateGameState(state);
    }

    /**
     * Gestisce il posizionamento di una carta
     * @param {Object} card - Carta da posizionare
     * @param {string} position - Posizione sul tabellone
     * @param {string} playerId - ID del giocatore (solo per local)
     */
    placeCard(card, position, playerId) {
        if (!this.currentManager) {
            console.error('[GAME MODE] Nessun manager attivo');
            return;
        }
        
        // Log per debug del problema della carta
        // console.log('[GAME MODE] Tentativo di piazzare carta:', card, 'in posizione:', position);
        
        if (this.currentMode === 'local') {
            // Verifica che la carta sia effettivamente nella mano del giocatore
            const playerData = this.currentManager.getPlayerData(playerId?.endsWith('_p1') ? 1 : 2);
            if (playerData && playerData.hand) {
                const hasCard = playerData.hand.some(handCard => 
                    handCard.suit === card.suit && String(handCard.value) === String(card.value)
                );
                
                if (!hasCard) {
                    console.error('[GAME MODE] Carta non trovata nella mano del giocatore!');
                    console.error('[GAME MODE] Mano attuale:', playerData.hand);
                    console.error('[GAME MODE] Carta tentata:', card);
                }
            }
            
            this.currentManager.placeCard(card, position, playerId);
        } else {
            this.currentManager.placeCard(card, position);
        }
    }

    /**
     * Gestisce la pesca di una carta
     * @param {string} playerId - ID del giocatore (solo per local)
     */
    drawCard(playerId) {
        if (!this.currentManager) {
            console.error('[GAME MODE] Nessun manager attivo');
            return;
        }
        
        if (this.currentMode === 'local') {
            this.currentManager.drawCard(playerId);
        } else {
            this.currentManager.drawCard();
        }
    }

    /**
     * Ottiene il manager corrente
     * @returns {LocalGameManager|OnlineGameManager|null}
     */
    getCurrentManager() {
        return this.currentManager;
    }

    /**
     * Ottiene la modalità corrente
     * @returns {string|null}
     */
    getCurrentMode() {
        return this.currentMode;
    }

    /**
     * Reset completo
     */
    reset() {
        if (this.currentManager) {
            this.currentManager.reset();
        }
        
        this.currentMode = null;
        this.currentManager = null;
        
        // console.log('[GAME MODE] Reset completo');
    }

    /**
     * Verifica se è il turno del giocatore corrente
     * @param {number} playerNumber - Numero del giocatore (solo per local)
     * @returns {boolean}
     */
    isMyTurn(playerNumber) {
        if (!this.currentManager) return false;
        
        if (this.currentMode === 'local') {
            return this.currentManager.isPlayerTurn(playerNumber);
        } else {
            return this.currentManager.isMyTurn();
        }
    }
}

// Crea un'istanza globale del manager
window.gameModeManager = new GameModeManager();

// console.log('[GAME MODE] Manager principale caricato e pronto');